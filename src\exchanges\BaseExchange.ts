/**
 * BaseExchange - Abstract Exchange Interface for AlphaMarketMaker
 * 
 * Provides a standardized interface for all cryptocurrency exchanges,
 * ensuring consistent behavior across Binance, Coinbase Pro, and Kraken.
 * 
 * Git Commit: "feat: add BaseExchange abstract class with standardized trading interface"
 */

import { EventEmitter } from 'events';
import { Logger } from '@/utils/Logger';
import { Config, ExchangeConfig } from '@/utils/Config';

export interface Ticker {
  symbol: string;
  bid: string;
  ask: string;
  last: string;
  volume: string;
  change: string;
  changePercent: string;
  timestamp: number;
}

export interface OrderBook {
  symbol: string;
  bids: [string, string][]; // [price, size]
  asks: [string, string][]; // [price, size]
  timestamp: number;
}

export interface Trade {
  id: string;
  symbol: string;
  side: 'buy' | 'sell';
  amount: string;
  price: string;
  timestamp: number;
}

export interface Order {
  id: string;
  clientOrderId?: string;
  symbol: string;
  side: 'buy' | 'sell';
  type: 'market' | 'limit' | 'stop' | 'stop-limit';
  amount: string;
  price?: string;
  filled: string;
  remaining: string;
  status: 'open' | 'closed' | 'canceled' | 'expired' | 'rejected';
  timestamp: number;
  fee?: {
    currency: string;
    cost: string;
  };
}

export interface Balance {
  currency: string;
  total: string;
  available: string;
  locked: string;
}

export interface Position {
  symbol: string;
  side: 'long' | 'short';
  size: string;
  entryPrice: string;
  markPrice: string;
  unrealizedPnl: string;
  percentage: string;
}

export interface OrderRequest {
  symbol: string;
  side: 'buy' | 'sell';
  type: 'market' | 'limit' | 'stop' | 'stop-limit';
  amount: string;
  price?: string;
  stopPrice?: string;
  clientOrderId?: string;
  timeInForce?: 'GTC' | 'IOC' | 'FOK';
}

export interface CancelOrderRequest {
  orderId: string;
  symbol: string;
  clientOrderId?: string;
}

export interface ExchangeInfo {
  name: string;
  rateLimits: {
    requests: number;
    interval: string;
  }[];
  symbols: {
    symbol: string;
    baseAsset: string;
    quoteAsset: string;
    status: string;
    minOrderSize: string;
    maxOrderSize: string;
    tickSize: string;
    stepSize: string;
  }[];
}

export interface MarketDataSubscription {
  symbol: string;
  channels: ('ticker' | 'orderbook' | 'trades')[];
}

/**
 * Abstract base class for all exchange implementations
 */
export abstract class BaseExchange extends EventEmitter {
  protected config: ExchangeConfig;
  protected logger: Logger;
  protected name: string;
  protected isConnected: boolean = false;
  protected rateLimiters: Map<string, number[]> = new Map();
  protected lastRequestTime: number = 0;
  protected requestCount: number = 0;

  constructor(name: string, config: ExchangeConfig, logger: Logger) {
    super();
    this.name = name;
    this.config = config;
    this.logger = logger.child({ 
      component: 'exchange',
      exchange: name 
    });
  }

  /**
   * Initialize exchange connection and authentication
   */
  public abstract initialize(): Promise<void>;

  /**
   * Close exchange connections and cleanup resources
   */
  public abstract close(): Promise<void>;

  /**
   * Get exchange information and trading rules
   */
  public abstract getExchangeInfo(): Promise<ExchangeInfo>;

  /**
   * Subscribe to real-time market data
   */
  public abstract subscribeToMarketData(subscriptions: MarketDataSubscription[]): Promise<void>;

  /**
   * Unsubscribe from market data
   */
  public abstract unsubscribeFromMarketData(symbols: string[]): Promise<void>;

  /**
   * Get current ticker information
   */
  public abstract getTicker(symbol: string): Promise<Ticker>;

  /**
   * Get order book for a symbol
   */
  public abstract getOrderBook(symbol: string, limit?: number): Promise<OrderBook>;

  /**
   * Get recent trades for a symbol
   */
  public abstract getTrades(symbol: string, limit?: number): Promise<Trade[]>;

  /**
   * Place a new order
   */
  public abstract placeOrder(order: OrderRequest): Promise<Order>;

  /**
   * Cancel an existing order
   */
  public abstract cancelOrder(request: CancelOrderRequest): Promise<boolean>;

  /**
   * Cancel all orders for a symbol
   */
  public abstract cancelAllOrders(symbol?: string): Promise<boolean>;

  /**
   * Get order status
   */
  public abstract getOrder(orderId: string, symbol: string): Promise<Order>;

  /**
   * Get open orders
   */
  public abstract getOpenOrders(symbol?: string): Promise<Order[]>;

  /**
   * Get order history
   */
  public abstract getOrderHistory(symbol?: string, limit?: number): Promise<Order[]>;

  /**
   * Get account balances
   */
  public abstract getBalances(): Promise<Balance[]>;

  /**
   * Get current positions (for margin/futures trading)
   */
  public abstract getPositions(): Promise<Position[]>;

  /**
   * Check if exchange supports a trading pair
   */
  public abstract supportsSymbol(symbol: string): Promise<boolean>;

  /**
   * Get minimum order size for a symbol
   */
  public abstract getMinOrderSize(symbol: string): Promise<string>;

  /**
   * Get trading fees for a symbol
   */
  public abstract getTradingFees(symbol: string): Promise<{ maker: string; taker: string }>;

  /**
   * Test connectivity and authentication
   */
  public abstract testConnection(): Promise<boolean>;

  /**
   * Get exchange name
   */
  public getName(): string {
    return this.name;
  }

  /**
   * Check if exchange is connected
   */
  public isExchangeConnected(): boolean {
    return this.isConnected;
  }

  /**
   * Get exchange configuration
   */
  public getConfig(): ExchangeConfig {
    return { ...this.config };
  }

  /**
   * Rate limiting implementation
   */
  protected async checkRateLimit(endpoint: string): Promise<void> {
    const now = Date.now();
    const windowMs = 60000; // 1 minute window
    const maxRequests = this.config.rateLimit || 1200;

    // Initialize rate limiter for endpoint if not exists
    if (!this.rateLimiters.has(endpoint)) {
      this.rateLimiters.set(endpoint, []);
    }

    const requests = this.rateLimiters.get(endpoint)!;
    
    // Remove old requests outside the window
    const validRequests = requests.filter(time => now - time < windowMs);
    
    // Check if we're at the limit
    if (validRequests.length >= maxRequests) {
      const oldestRequest = Math.min(...validRequests);
      const waitTime = windowMs - (now - oldestRequest);
      
      this.logger.warn(`Rate limit reached for ${endpoint}, waiting ${waitTime}ms`, {
        endpoint,
        requestCount: validRequests.length,
        maxRequests
      });
      
      await this.sleep(waitTime);
    }

    // Add current request
    validRequests.push(now);
    this.rateLimiters.set(endpoint, validRequests);
  }

  /**
   * Sleep utility for rate limiting
   */
  protected sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Normalize symbol format across exchanges
   */
  protected normalizeSymbol(symbol: string): string {
    // Convert from standard format (BTC/USD) to exchange-specific format
    return symbol.replace('/', '').toUpperCase();
  }

  /**
   * Denormalize symbol format to standard format
   */
  protected denormalizeSymbol(symbol: string): string {
    // Convert from exchange format to standard format (BTC/USD)
    // This should be overridden by each exchange implementation
    return symbol;
  }

  /**
   * Handle WebSocket connection events
   */
  protected handleWebSocketOpen(): void {
    this.isConnected = true;
    this.logger.info(`${this.name} WebSocket connected`);
    this.emit('connected');
  }

  /**
   * Handle WebSocket disconnection events
   */
  protected handleWebSocketClose(): void {
    this.isConnected = false;
    this.logger.warn(`${this.name} WebSocket disconnected`);
    this.emit('disconnected');
  }

  /**
   * Handle WebSocket errors
   */
  protected handleWebSocketError(error: Error): void {
    this.logger.error(`${this.name} WebSocket error:`, error);
    this.emit('error', error);
  }

  /**
   * Handle ticker updates
   */
  protected handleTickerUpdate(ticker: Ticker): void {
    this.logger.market(`Ticker update received`, {
      exchange: this.name,
      symbol: ticker.symbol,
      price: ticker.last,
      spread: (parseFloat(ticker.ask) - parseFloat(ticker.bid)).toString()
    });
    this.emit('ticker', ticker);
  }

  /**
   * Handle order book updates
   */
  protected handleOrderBookUpdate(orderBook: OrderBook): void {
    this.logger.market(`Order book update received`, {
      exchange: this.name,
      symbol: orderBook.symbol,
      bidCount: orderBook.bids.length,
      askCount: orderBook.asks.length
    });
    this.emit('orderbook', orderBook);
  }

  /**
   * Handle trade updates
   */
  protected handleTradeUpdate(trade: Trade): void {
    this.logger.market(`Trade update received`, {
      exchange: this.name,
      symbol: trade.symbol,
      side: trade.side,
      amount: trade.amount,
      price: trade.price
    });
    this.emit('trade', trade);
  }

  /**
   * Handle order updates
   */
  protected handleOrderUpdate(order: Order): void {
    this.logger.order(`Order update received`, {
      exchange: this.name,
      symbol: order.symbol,
      side: order.side,
      amount: order.amount,
      price: order.price || '0',
      orderId: order.id,
      status: order.status
    });
    this.emit('order', order);
  }

  /**
   * Validate order request
   */
  protected validateOrderRequest(order: OrderRequest): void {
    if (!order.symbol || !order.side || !order.type || !order.amount) {
      throw new Error('Missing required order parameters');
    }

    if (order.type === 'limit' && !order.price) {
      throw new Error('Limit orders require a price');
    }

    if (parseFloat(order.amount) <= 0) {
      throw new Error('Order amount must be positive');
    }

    if (order.price && parseFloat(order.price) <= 0) {
      throw new Error('Order price must be positive');
    }
  }

  /**
   * Generate client order ID
   */
  protected generateClientOrderId(): string {
    return `AMM_${this.name}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Format number to exchange precision
   */
  protected formatNumber(value: string | number, precision: number): string {
    return parseFloat(value.toString()).toFixed(precision);
  }

  /**
   * Get current timestamp in milliseconds
   */
  protected getCurrentTimestamp(): number {
    return Date.now();
  }
}
