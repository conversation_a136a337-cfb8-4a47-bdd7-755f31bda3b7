/**
 * Cache Management for AlphaMarketMaker
 * 
 * Redis-based caching system for market data, order books, and trading state
 * with automatic expiration, compression, and high-performance access patterns.
 * 
 * Git Commit: "feat: add Redis-based CacheManager for market data caching"
 */

import Redis from 'redis';
import { Config } from '@/utils/Config';
import { Logger } from '@/utils/Logger';
import { Ticker, OrderBook, Trade } from '@/exchanges/BaseExchange';

export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  compress?: boolean; // Enable compression for large data
  serialize?: boolean; // Auto serialize/deserialize JSON
}

export interface CacheStats {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  memory: string;
  connections: number;
}

export class CacheManager {
  private client: Redis.RedisClientType;
  private config: Config;
  private logger: Logger;
  private isInitialized: boolean = false;
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    memory: '0',
    connections: 0
  };

  // Cache key prefixes
  private static readonly PREFIXES = {
    TICKER: 'ticker:',
    ORDERBOOK: 'orderbook:',
    TRADES: 'trades:',
    BALANCE: 'balance:',
    POSITION: 'position:',
    ORDER: 'order:',
    MARKET_DATA: 'market:',
    USER_SESSION: 'session:',
    RATE_LIMIT: 'ratelimit:',
    HEALTH: 'health:'
  };

  constructor(config: Config, logger: Logger) {
    this.config = config;
    this.logger = logger.child({ component: 'cache' });
  }

  /**
   * Initialize Redis connection
   */
  public async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing Redis cache connection...');

      const cacheConfig = this.config.getCache();
      
      this.client = Redis.createClient({
        url: cacheConfig.url,
        socket: {
          connectTimeout: 10000,
          lazyConnect: true,
          reconnectStrategy: (retries) => {
            if (retries > 10) {
              this.logger.error('Redis reconnection failed after 10 attempts');
              return false;
            }
            const delay = Math.min(retries * 100, 3000);
            this.logger.warn(`Redis reconnecting in ${delay}ms (attempt ${retries})`);
            return delay;
          }
        }
      });

      // Set up event handlers
      this.client.on('connect', () => {
        this.logger.info('Redis client connected');
      });

      this.client.on('ready', () => {
        this.isInitialized = true;
        this.logger.info('Redis client ready');
      });

      this.client.on('error', (error) => {
        this.logger.error('Redis client error:', error);
      });

      this.client.on('end', () => {
        this.isInitialized = false;
        this.logger.warn('Redis client connection ended');
      });

      // Connect to Redis
      await this.client.connect();

      // Test connection
      await this.testConnection();

      // Configure memory settings if specified
      if (cacheConfig.maxMemory) {
        await this.client.configSet('maxmemory', cacheConfig.maxMemory);
        await this.client.configSet('maxmemory-policy', 'allkeys-lru');
      }

      this.logger.info('Cache manager initialized successfully');

    } catch (error) {
      this.logger.error('Failed to initialize cache manager:', error);
      throw error;
    }
  }

  /**
   * Test Redis connection
   */
  private async testConnection(): Promise<void> {
    try {
      const result = await this.client.ping();
      if (result !== 'PONG') {
        throw new Error('Redis ping test failed');
      }
      this.logger.info('Redis connection test successful');
    } catch (error) {
      this.logger.error('Redis connection test failed:', error);
      throw error;
    }
  }

  /**
   * Set cache value with optional TTL
   */
  public async set(
    key: string, 
    value: any, 
    options: CacheOptions = {}
  ): Promise<void> {
    try {
      const { ttl, serialize = true } = options;
      const serializedValue = serialize ? JSON.stringify(value) : value;
      
      if (ttl) {
        await this.client.setEx(key, ttl, serializedValue);
      } else {
        await this.client.set(key, serializedValue);
      }

      this.stats.sets++;
      this.logger.debug(`Cache set: ${key}`, { ttl });
    } catch (error) {
      this.logger.error(`Failed to set cache key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Get cache value
   */
  public async get<T = any>(
    key: string, 
    options: CacheOptions = {}
  ): Promise<T | null> {
    try {
      const { serialize = true } = options;
      const value = await this.client.get(key);
      
      if (value === null) {
        this.stats.misses++;
        return null;
      }

      this.stats.hits++;
      this.logger.debug(`Cache hit: ${key}`);
      
      return serialize ? JSON.parse(value) : value;
    } catch (error) {
      this.logger.error(`Failed to get cache key ${key}:`, error);
      this.stats.misses++;
      return null;
    }
  }

  /**
   * Delete cache key
   */
  public async delete(key: string): Promise<boolean> {
    try {
      const result = await this.client.del(key);
      this.stats.deletes++;
      this.logger.debug(`Cache delete: ${key}`);
      return result > 0;
    } catch (error) {
      this.logger.error(`Failed to delete cache key ${key}:`, error);
      return false;
    }
  }

  /**
   * Check if key exists
   */
  public async exists(key: string): Promise<boolean> {
    try {
      const result = await this.client.exists(key);
      return result > 0;
    } catch (error) {
      this.logger.error(`Failed to check cache key existence ${key}:`, error);
      return false;
    }
  }

  /**
   * Set key expiration
   */
  public async expire(key: string, seconds: number): Promise<boolean> {
    try {
      const result = await this.client.expire(key, seconds);
      return result;
    } catch (error) {
      this.logger.error(`Failed to set expiration for key ${key}:`, error);
      return false;
    }
  }

  /**
   * Cache ticker data
   */
  public async setTicker(exchange: string, symbol: string, ticker: Ticker): Promise<void> {
    const key = `${CacheManager.PREFIXES.TICKER}${exchange}:${symbol}`;
    await this.set(key, ticker, { ttl: 5 }); // 5 second TTL for tickers
  }

  /**
   * Get cached ticker data
   */
  public async getTicker(exchange: string, symbol: string): Promise<Ticker | null> {
    const key = `${CacheManager.PREFIXES.TICKER}${exchange}:${symbol}`;
    return await this.get<Ticker>(key);
  }

  /**
   * Cache order book data
   */
  public async setOrderBook(exchange: string, symbol: string, orderBook: OrderBook): Promise<void> {
    const key = `${CacheManager.PREFIXES.ORDERBOOK}${exchange}:${symbol}`;
    await this.set(key, orderBook, { ttl: 2 }); // 2 second TTL for order books
  }

  /**
   * Get cached order book data
   */
  public async getOrderBook(exchange: string, symbol: string): Promise<OrderBook | null> {
    const key = `${CacheManager.PREFIXES.ORDERBOOK}${exchange}:${symbol}`;
    return await this.get<OrderBook>(key);
  }

  /**
   * Cache recent trades
   */
  public async setTrades(exchange: string, symbol: string, trades: Trade[]): Promise<void> {
    const key = `${CacheManager.PREFIXES.TRADES}${exchange}:${symbol}`;
    await this.set(key, trades, { ttl: 10 }); // 10 second TTL for trades
  }

  /**
   * Get cached trades
   */
  public async getTrades(exchange: string, symbol: string): Promise<Trade[] | null> {
    const key = `${CacheManager.PREFIXES.TRADES}${exchange}:${symbol}`;
    return await this.get<Trade[]>(key);
  }

  /**
   * Cache market data aggregation
   */
  public async setMarketData(key: string, data: any, ttl: number = 60): Promise<void> {
    const fullKey = `${CacheManager.PREFIXES.MARKET_DATA}${key}`;
    await this.set(fullKey, data, { ttl });
  }

  /**
   * Get cached market data
   */
  public async getMarketData<T = any>(key: string): Promise<T | null> {
    const fullKey = `${CacheManager.PREFIXES.MARKET_DATA}${key}`;
    return await this.get<T>(fullKey);
  }

  /**
   * Implement rate limiting using Redis
   */
  public async checkRateLimit(
    identifier: string, 
    limit: number, 
    windowSeconds: number
  ): Promise<{ allowed: boolean; remaining: number; resetTime: number }> {
    try {
      const key = `${CacheManager.PREFIXES.RATE_LIMIT}${identifier}`;
      const now = Date.now();
      const windowStart = now - (windowSeconds * 1000);

      // Use Redis sorted set for sliding window rate limiting
      const pipeline = this.client.multi();
      
      // Remove old entries
      pipeline.zRemRangeByScore(key, 0, windowStart);
      
      // Count current requests
      pipeline.zCard(key);
      
      // Add current request
      pipeline.zAdd(key, { score: now, value: now.toString() });
      
      // Set expiration
      pipeline.expire(key, windowSeconds);
      
      const results = await pipeline.exec();
      const currentCount = results[1] as number;
      
      const allowed = currentCount < limit;
      const remaining = Math.max(0, limit - currentCount - 1);
      const resetTime = now + (windowSeconds * 1000);

      return { allowed, remaining, resetTime };
    } catch (error) {
      this.logger.error(`Rate limit check failed for ${identifier}:`, error);
      // Fail open - allow request if Redis is down
      return { allowed: true, remaining: limit - 1, resetTime: Date.now() + (windowSeconds * 1000) };
    }
  }

  /**
   * Store user session data
   */
  public async setSession(sessionId: string, data: any, ttl: number = 86400): Promise<void> {
    const key = `${CacheManager.PREFIXES.USER_SESSION}${sessionId}`;
    await this.set(key, data, { ttl });
  }

  /**
   * Get user session data
   */
  public async getSession<T = any>(sessionId: string): Promise<T | null> {
    const key = `${CacheManager.PREFIXES.USER_SESSION}${sessionId}`;
    return await this.get<T>(key);
  }

  /**
   * Delete user session
   */
  public async deleteSession(sessionId: string): Promise<boolean> {
    const key = `${CacheManager.PREFIXES.USER_SESSION}${sessionId}`;
    return await this.delete(key);
  }

  /**
   * Get cache statistics
   */
  public async getStats(): Promise<CacheStats> {
    try {
      const info = await this.client.info('memory');
      const memoryMatch = info.match(/used_memory_human:(.+)/);
      const memory = memoryMatch ? memoryMatch[1].trim() : '0';

      const clientInfo = await this.client.info('clients');
      const connectionsMatch = clientInfo.match(/connected_clients:(\d+)/);
      const connections = connectionsMatch ? parseInt(connectionsMatch[1], 10) : 0;

      return {
        ...this.stats,
        memory,
        connections
      };
    } catch (error) {
      this.logger.error('Failed to get cache stats:', error);
      return this.stats;
    }
  }

  /**
   * Clear all cache data (use with caution)
   */
  public async flushAll(): Promise<void> {
    try {
      await this.client.flushAll();
      this.logger.warn('All cache data cleared');
    } catch (error) {
      this.logger.error('Failed to flush cache:', error);
      throw error;
    }
  }

  /**
   * Get keys matching pattern
   */
  public async getKeys(pattern: string): Promise<string[]> {
    try {
      return await this.client.keys(pattern);
    } catch (error) {
      this.logger.error(`Failed to get keys for pattern ${pattern}:`, error);
      return [];
    }
  }

  /**
   * Batch operations
   */
  public async mget(keys: string[]): Promise<(string | null)[]> {
    try {
      return await this.client.mGet(keys);
    } catch (error) {
      this.logger.error('Failed to execute mget:', error);
      return new Array(keys.length).fill(null);
    }
  }

  /**
   * Health check
   */
  public async healthCheck(): Promise<{ status: 'healthy' | 'unhealthy'; latency: number }> {
    try {
      const start = Date.now();
      await this.client.ping();
      const latency = Date.now() - start;
      
      return { status: 'healthy', latency };
    } catch (error) {
      this.logger.error('Cache health check failed:', error);
      return { status: 'unhealthy', latency: -1 };
    }
  }

  /**
   * Close Redis connection
   */
  public async close(): Promise<void> {
    if (this.client) {
      await this.client.quit();
      this.logger.info('Cache manager connection closed');
    }
  }

  /**
   * Check if cache is ready
   */
  public isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * Get Redis client instance (for advanced operations)
   */
  public getClient(): Redis.RedisClientType {
    return this.client;
  }
}
