/**
 * Market Making Engine for AlphaMarketMaker
 * 
 * Core market making algorithm with dynamic spread calculation based on volatility,
 * order book depth, inventory management, and two-sided liquidity provision.
 * 
 * Git Commit: "feat: add MarketMaker core engine with dynamic spread calculation"
 */

import { EventEmitter } from 'events';
import Big from 'big.js';
import { Config } from '@/utils/Config';
import { Logger } from '@/utils/Logger';
import { MarketDataManager, AggregatedTicker, AggregatedOrderBook } from '@/data/MarketDataManager';
import { RiskManager } from './RiskManager';
import { OrderManager } from './OrderManager';
import { PortfolioTracker } from './PortfolioTracker';

export interface MarketMakingConfig {
  symbol: string;
  enabled: boolean;
  exchanges: string[];
  baseSpread: number; // Base spread in basis points
  maxSpread: number; // Maximum spread in basis points
  minSpread: number; // Minimum spread in basis points
  orderSize: string; // Order size in base currency
  maxPosition: string; // Maximum position size
  inventoryTarget: string; // Target inventory (0 = neutral)
  refreshInterval: number; // Order refresh interval in ms
  volatilityWindow: number; // Volatility calculation window
  depthWeight: number; // Order book depth weight factor
  inventorySkew: boolean; // Enable inventory skewing
}

export interface QuoteParameters {
  symbol: string;
  bidPrice: string;
  askPrice: string;
  bidSize: string;
  askSize: string;
  spread: number; // in basis points
  skew: number; // inventory skew factor
  confidence: number; // quote confidence (0-1)
  timestamp: number;
}

export interface MarketMakingState {
  symbol: string;
  isActive: boolean;
  currentQuotes: QuoteParameters | null;
  lastUpdate: number;
  ordersPlaced: number;
  ordersFilled: number;
  totalVolume: string;
  pnl: string;
  inventory: string;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
}

export class MarketMaker extends EventEmitter {
  private config: Config;
  private logger: Logger;
  private marketDataManager: MarketDataManager;
  private riskManager: RiskManager;
  private orderManager: OrderManager;
  private portfolioTracker: PortfolioTracker;

  private isRunning: boolean = false;
  private activeSymbols: Map<string, MarketMakingConfig> = new Map();
  private marketStates: Map<string, MarketMakingState> = new Map();
  private refreshIntervals: Map<string, NodeJS.Timeout> = new Map();
  private priceHistory: Map<string, number[]> = new Map();

  // Market making parameters
  private readonly VOLATILITY_LOOKBACK = 100; // Number of price points for volatility
  private readonly MIN_CONFIDENCE = 0.3; // Minimum confidence to place quotes
  private readonly MAX_INVENTORY_SKEW = 0.5; // Maximum inventory skew factor

  constructor(
    config: Config,
    logger: Logger,
    marketDataManager: MarketDataManager,
    riskManager: RiskManager,
    orderManager: OrderManager,
    portfolioTracker: PortfolioTracker
  ) {
    super();
    this.config = config;
    this.logger = logger.child({ component: 'market-maker' });
    this.marketDataManager = marketDataManager;
    this.riskManager = riskManager;
    this.orderManager = orderManager;
    this.portfolioTracker = portfolioTracker;

    this.setupEventListeners();
  }

  /**
   * Setup event listeners for market data and risk events
   */
  private setupEventListeners(): void {
    // Market data events
    this.marketDataManager.on('ticker', (exchange: string, ticker: any) => {
      this.handleTickerUpdate(ticker);
    });

    this.marketDataManager.on('orderbook', (exchange: string, orderBook: any) => {
      this.handleOrderBookUpdate(orderBook);
    });

    // Risk management events
    this.riskManager.on('riskAlert', (alert: any) => {
      this.handleRiskAlert(alert);
    });

    this.riskManager.on('positionLimit', (symbol: string) => {
      this.handlePositionLimit(symbol);
    });

    // Order events
    this.orderManager.on('orderFilled', (order: any) => {
      this.handleOrderFilled(order);
    });

    this.orderManager.on('orderCanceled', (order: any) => {
      this.handleOrderCanceled(order);
    });
  }

  /**
   * Start market making for specified symbols
   */
  public async start(symbols: string[]): Promise<void> {
    try {
      this.logger.info('Starting market making engine', { symbols });

      if (this.isRunning) {
        throw new Error('Market maker is already running');
      }

      // Validate symbols and create configurations
      for (const symbol of symbols) {
        const config = await this.createMarketMakingConfig(symbol);
        this.activeSymbols.set(symbol, config);
        
        // Initialize market state
        this.marketStates.set(symbol, {
          symbol,
          isActive: false,
          currentQuotes: null,
          lastUpdate: 0,
          ordersPlaced: 0,
          ordersFilled: 0,
          totalVolume: '0',
          pnl: '0',
          inventory: '0',
          riskLevel: 'low'
        });

        // Initialize price history
        this.priceHistory.set(symbol, []);
      }

      // Subscribe to market data
      await this.marketDataManager.subscribeToSymbols(symbols);

      // Start market making loops for each symbol
      for (const symbol of symbols) {
        await this.startMarketMakingLoop(symbol);
      }

      this.isRunning = true;
      this.emit('started', symbols);
      this.logger.info('Market making engine started successfully');

    } catch (error) {
      this.logger.error('Failed to start market making engine:', error);
      throw error;
    }
  }

  /**
   * Stop market making
   */
  public async stop(): Promise<void> {
    try {
      this.logger.info('Stopping market making engine');

      if (!this.isRunning) {
        return;
      }

      // Stop all refresh intervals
      for (const interval of this.refreshIntervals.values()) {
        clearInterval(interval);
      }
      this.refreshIntervals.clear();

      // Cancel all open orders
      for (const symbol of this.activeSymbols.keys()) {
        await this.orderManager.cancelAllOrders(symbol);
        
        const state = this.marketStates.get(symbol);
        if (state) {
          state.isActive = false;
        }
      }

      // Unsubscribe from market data
      const symbols = Array.from(this.activeSymbols.keys());
      await this.marketDataManager.unsubscribeFromSymbols(symbols);

      this.isRunning = false;
      this.activeSymbols.clear();
      this.marketStates.clear();
      this.priceHistory.clear();

      this.emit('stopped');
      this.logger.info('Market making engine stopped successfully');

    } catch (error) {
      this.logger.error('Failed to stop market making engine:', error);
      throw error;
    }
  }

  /**
   * Create market making configuration for a symbol
   */
  private async createMarketMakingConfig(symbol: string): Promise<MarketMakingConfig> {
    // Get default configuration from config file
    const marketMakingConfig = this.config.getMarketMaking();
    const riskLimits = this.config.getRiskLimits();

    return {
      symbol,
      enabled: true,
      exchanges: this.marketDataManager.getConnectedExchanges(),
      baseSpread: 10, // 10 basis points
      maxSpread: 100, // 100 basis points
      minSpread: 5, // 5 basis points
      orderSize: '0.01', // Default order size
      maxPosition: riskLimits.maxPositionSize.toString(),
      inventoryTarget: '0', // Neutral inventory
      refreshInterval: marketMakingConfig.orderManagement.refreshInterval,
      volatilityWindow: marketMakingConfig.spreadCalculation.volatilityWindow,
      depthWeight: marketMakingConfig.spreadCalculation.depthWeight,
      inventorySkew: marketMakingConfig.spreadCalculation.inventorySkew
    };
  }

  /**
   * Start market making loop for a symbol
   */
  private async startMarketMakingLoop(symbol: string): Promise<void> {
    const config = this.activeSymbols.get(symbol);
    if (!config) {
      return;
    }

    // Initial quote placement
    await this.updateQuotes(symbol);

    // Set up refresh interval
    const interval = setInterval(async () => {
      try {
        await this.updateQuotes(symbol);
      } catch (error) {
        this.logger.error(`Failed to update quotes for ${symbol}:`, error);
      }
    }, config.refreshInterval);

    this.refreshIntervals.set(symbol, interval);

    // Mark as active
    const state = this.marketStates.get(symbol);
    if (state) {
      state.isActive = true;
    }

    this.logger.info(`Market making loop started for ${symbol}`);
  }

  /**
   * Update quotes for a symbol
   */
  private async updateQuotes(symbol: string): Promise<void> {
    try {
      const config = this.activeSymbols.get(symbol);
      const state = this.marketStates.get(symbol);
      
      if (!config || !state || !state.isActive) {
        return;
      }

      // Get market data
      const ticker = this.marketDataManager.getAggregatedTicker(symbol);
      const orderBook = this.marketDataManager.getAggregatedOrderBook(symbol);

      if (!ticker || !orderBook) {
        this.logger.warn(`Insufficient market data for ${symbol}`);
        return;
      }

      // Calculate quote parameters
      const quoteParams = await this.calculateQuoteParameters(symbol, ticker, orderBook, config);

      if (!quoteParams || quoteParams.confidence < this.MIN_CONFIDENCE) {
        this.logger.warn(`Low confidence quotes for ${symbol}, skipping`, {
          confidence: quoteParams?.confidence
        });
        return;
      }

      // Check risk limits
      const riskCheck = await this.riskManager.checkOrderRisk(symbol, {
        side: 'buy',
        amount: quoteParams.bidSize,
        price: quoteParams.bidPrice
      });

      if (!riskCheck.allowed) {
        this.logger.warn(`Risk limits exceeded for ${symbol}:`, riskCheck.reason);
        return;
      }

      // Cancel existing orders
      await this.orderManager.cancelAllOrders(symbol);

      // Place new quotes
      await this.placeQuotes(symbol, quoteParams);

      // Update state
      state.currentQuotes = quoteParams;
      state.lastUpdate = Date.now();
      state.ordersPlaced += 2; // bid and ask

      this.emit('quotesUpdated', symbol, quoteParams);

    } catch (error) {
      this.logger.error(`Failed to update quotes for ${symbol}:`, error);
    }
  }

  /**
   * Calculate quote parameters based on market conditions
   */
  private async calculateQuoteParameters(
    symbol: string,
    ticker: AggregatedTicker,
    orderBook: AggregatedOrderBook,
    config: MarketMakingConfig
  ): Promise<QuoteParameters | null> {
    try {
      // Get mid price
      const bestBid = new Big(ticker.bestBid.price);
      const bestAsk = new Big(ticker.bestAsk.price);
      const midPrice = bestBid.plus(bestAsk).div(2);

      // Calculate volatility-based spread
      const volatility = this.calculateVolatility(symbol);
      const volatilitySpread = Math.max(volatility * 10000, config.minSpread); // Convert to basis points

      // Calculate depth-based spread adjustment
      const depthAdjustment = this.calculateDepthAdjustment(orderBook, config.depthWeight);

      // Calculate inventory skew
      const inventorySkew = config.inventorySkew 
        ? await this.calculateInventorySkew(symbol, config)
        : 0;

      // Combine spread components
      let totalSpread = Math.min(
        Math.max(
          config.baseSpread + volatilitySpread + depthAdjustment,
          config.minSpread
        ),
        config.maxSpread
      );

      // Apply inventory skew to prices
      const spreadBps = totalSpread / 10000; // Convert to decimal
      const halfSpread = midPrice.mul(spreadBps / 2);

      const bidPrice = midPrice.minus(halfSpread).plus(midPrice.mul(inventorySkew));
      const askPrice = midPrice.plus(halfSpread).plus(midPrice.mul(inventorySkew));

      // Calculate order sizes (could be adjusted based on market conditions)
      const bidSize = config.orderSize;
      const askSize = config.orderSize;

      // Calculate confidence based on market conditions
      const confidence = this.calculateQuoteConfidence(ticker, orderBook, volatility);

      return {
        symbol,
        bidPrice: bidPrice.toFixed(8),
        askPrice: askPrice.toFixed(8),
        bidSize,
        askSize,
        spread: totalSpread,
        skew: inventorySkew,
        confidence,
        timestamp: Date.now()
      };

    } catch (error) {
      this.logger.error(`Failed to calculate quote parameters for ${symbol}:`, error);
      return null;
    }
  }

  /**
   * Calculate volatility based on recent price history
   */
  private calculateVolatility(symbol: string): number {
    const prices = this.priceHistory.get(symbol) || [];
    
    if (prices.length < 2) {
      return 0.001; // Default low volatility
    }

    // Calculate returns
    const returns = prices.slice(1).map((price, i) => 
      (price - prices[i]) / prices[i]
    );

    if (returns.length === 0) {
      return 0.001;
    }

    // Calculate standard deviation
    const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / returns.length;
    
    return Math.sqrt(variance);
  }

  /**
   * Calculate depth-based spread adjustment
   */
  private calculateDepthAdjustment(orderBook: AggregatedOrderBook, depthWeight: number): number {
    try {
      const bidDepth = new Big(orderBook.depth.bids);
      const askDepth = new Big(orderBook.depth.asks);
      const totalDepth = bidDepth.plus(askDepth);

      if (totalDepth.eq(0)) {
        return 20; // High spread for low liquidity
      }

      // Lower depth = higher spread
      const depthFactor = new Big(1000).div(totalDepth); // Adjust base value as needed
      return Math.min(depthFactor.mul(depthWeight).toNumber(), 50); // Cap at 50 bps

    } catch (error) {
      return 10; // Default adjustment
    }
  }

  /**
   * Calculate inventory skew factor
   */
  private async calculateInventorySkew(symbol: string, config: MarketMakingConfig): Promise<number> {
    try {
      const position = await this.portfolioTracker.getPosition(symbol);
      if (!position) {
        return 0;
      }

      const currentInventory = new Big(position.size);
      const targetInventory = new Big(config.inventoryTarget);
      const maxPosition = new Big(config.maxPosition);

      // Calculate inventory deviation from target
      const inventoryDeviation = currentInventory.minus(targetInventory);
      const normalizedDeviation = inventoryDeviation.div(maxPosition);

      // Apply skew (negative inventory = positive skew to encourage buying)
      const skew = normalizedDeviation.mul(-1).mul(this.MAX_INVENTORY_SKEW);

      return Math.max(-this.MAX_INVENTORY_SKEW, Math.min(this.MAX_INVENTORY_SKEW, skew.toNumber()));

    } catch (error) {
      this.logger.error(`Failed to calculate inventory skew for ${symbol}:`, error);
      return 0;
    }
  }

  /**
   * Calculate quote confidence based on market conditions
   */
  private calculateQuoteConfidence(
    ticker: AggregatedTicker,
    orderBook: AggregatedOrderBook,
    volatility: number
  ): number {
    let confidence = 1.0;

    // Reduce confidence for high volatility
    if (volatility > 0.02) { // 2% volatility threshold
      confidence *= 0.7;
    }

    // Reduce confidence for wide spreads
    const spreadBps = ticker.spreadBps;
    if (spreadBps > 50) { // 50 basis points threshold
      confidence *= 0.8;
    }

    // Reduce confidence for low liquidity
    const totalDepth = new Big(orderBook.depth.bids).plus(orderBook.depth.asks);
    if (totalDepth.lt(100)) { // Adjust threshold as needed
      confidence *= 0.6;
    }

    // Reduce confidence if fewer exchanges are providing data
    const exchangeCount = Object.keys(ticker.exchanges).length;
    if (exchangeCount < 2) {
      confidence *= 0.5;
    }

    return Math.max(0, Math.min(1, confidence));
  }

  /**
   * Place bid and ask quotes
   */
  private async placeQuotes(symbol: string, params: QuoteParameters): Promise<void> {
    try {
      // Place bid order
      const bidOrder = await this.orderManager.placeOrder({
        symbol,
        side: 'buy',
        type: 'limit',
        amount: params.bidSize,
        price: params.bidPrice,
        timeInForce: 'GTC'
      });

      // Place ask order
      const askOrder = await this.orderManager.placeOrder({
        symbol,
        side: 'sell',
        type: 'limit',
        amount: params.askSize,
        price: params.askPrice,
        timeInForce: 'GTC'
      });

      this.logger.info(`Quotes placed for ${symbol}`, {
        bidPrice: params.bidPrice,
        askPrice: params.askPrice,
        spread: params.spread,
        bidOrderId: bidOrder.id,
        askOrderId: askOrder.id
      });

    } catch (error) {
      this.logger.error(`Failed to place quotes for ${symbol}:`, error);
      throw error;
    }
  }

  /**
   * Handle ticker update
   */
  private handleTickerUpdate(ticker: any): void {
    const prices = this.priceHistory.get(ticker.symbol) || [];
    prices.push(parseFloat(ticker.last));

    // Keep only recent prices for volatility calculation
    if (prices.length > this.VOLATILITY_LOOKBACK) {
      prices.splice(0, prices.length - this.VOLATILITY_LOOKBACK);
    }

    this.priceHistory.set(ticker.symbol, prices);
  }

  /**
   * Handle order book update
   */
  private handleOrderBookUpdate(orderBook: any): void {
    // Order book updates are handled by the market data manager
    // We could add additional processing here if needed
  }

  /**
   * Handle risk alert
   */
  private handleRiskAlert(alert: any): void {
    this.logger.warn('Risk alert received', alert);
    
    if (alert.severity === 'critical') {
      // Stop market making for the affected symbol
      this.pauseMarketMaking(alert.symbol);
    }
  }

  /**
   * Handle position limit reached
   */
  private handlePositionLimit(symbol: string): void {
    this.logger.warn(`Position limit reached for ${symbol}, pausing market making`);
    this.pauseMarketMaking(symbol);
  }

  /**
   * Handle order filled
   */
  private handleOrderFilled(order: any): void {
    const state = this.marketStates.get(order.symbol);
    if (state) {
      state.ordersFilled++;
      state.totalVolume = new Big(state.totalVolume).plus(order.filled).toString();
    }

    this.logger.info('Market making order filled', {
      symbol: order.symbol,
      side: order.side,
      amount: order.filled,
      price: order.price
    });

    this.emit('orderFilled', order);
  }

  /**
   * Handle order canceled
   */
  private handleOrderCanceled(order: any): void {
    this.logger.debug('Market making order canceled', {
      symbol: order.symbol,
      orderId: order.id
    });
  }

  /**
   * Pause market making for a symbol
   */
  private async pauseMarketMaking(symbol: string): Promise<void> {
    const state = this.marketStates.get(symbol);
    if (state) {
      state.isActive = false;
    }

    // Cancel orders
    await this.orderManager.cancelAllOrders(symbol);

    // Clear refresh interval
    const interval = this.refreshIntervals.get(symbol);
    if (interval) {
      clearInterval(interval);
      this.refreshIntervals.delete(symbol);
    }

    this.logger.info(`Market making paused for ${symbol}`);
    this.emit('paused', symbol);
  }

  /**
   * Resume market making for a symbol
   */
  public async resumeMarketMaking(symbol: string): Promise<void> {
    const config = this.activeSymbols.get(symbol);
    const state = this.marketStates.get(symbol);

    if (!config || !state) {
      throw new Error(`Symbol ${symbol} not configured for market making`);
    }

    await this.startMarketMakingLoop(symbol);
    this.logger.info(`Market making resumed for ${symbol}`);
    this.emit('resumed', symbol);
  }

  /**
   * Get market making state for a symbol
   */
  public getMarketState(symbol: string): MarketMakingState | null {
    return this.marketStates.get(symbol) || null;
  }

  /**
   * Get all market states
   */
  public getAllMarketStates(): MarketMakingState[] {
    return Array.from(this.marketStates.values());
  }

  /**
   * Check if market maker is running
   */
  public isActive(): boolean {
    return this.isRunning;
  }

  /**
   * Get active symbols
   */
  public getActiveSymbols(): string[] {
    return Array.from(this.activeSymbols.keys());
  }
}
