/**
 * Market Data Manager for AlphaMarketMaker
 * 
 * Aggregates real-time market data from multiple exchanges, provides unified
 * price feeds, order book management, and market data analytics.
 * 
 * Git Commit: "feat: add MarketDataManager for multi-exchange data aggregation"
 */

import { EventEmitter } from 'events';
import { Config } from '@/utils/Config';
import { Logger } from '@/utils/Logger';
import { CacheManager } from './CacheManager';
import { BaseExchange, Ticker, OrderBook, Trade, MarketDataSubscription } from '@/exchanges/BaseExchange';
import { BinanceExchange } from '@/exchanges/BinanceExchange';
import { CoinbaseExchange } from '@/exchanges/CoinbaseExchange';
// import { KrakenExchange } from '@/exchanges/KrakenExchange'; // Will be implemented

export interface AggregatedTicker {
  symbol: string;
  exchanges: {
    [exchangeName: string]: {
      bid: string;
      ask: string;
      last: string;
      volume: string;
      timestamp: number;
    };
  };
  bestBid: {
    price: string;
    exchange: string;
  };
  bestAsk: {
    price: string;
    exchange: string;
  };
  weightedPrice: string;
  totalVolume: string;
  spread: string;
  spreadBps: number;
  timestamp: number;
}

export interface AggregatedOrderBook {
  symbol: string;
  bids: Array<{
    price: string;
    size: string;
    exchange: string;
    timestamp: number;
  }>;
  asks: Array<{
    price: string;
    size: string;
    exchange: string;
    timestamp: number;
  }>;
  bestBid: string;
  bestAsk: string;
  spread: string;
  depth: {
    bids: string; // Total bid volume
    asks: string; // Total ask volume
  };
  timestamp: number;
}

export interface MarketStats {
  symbol: string;
  price24hChange: string;
  price24hChangePercent: string;
  high24h: string;
  low24h: string;
  volume24h: string;
  volatility: string;
  averageSpread: string;
  exchangeCount: number;
  lastUpdate: number;
}

export class MarketDataManager extends EventEmitter {
  private config: Config;
  private logger: Logger;
  private cacheManager: CacheManager;
  private exchanges: Map<string, BaseExchange> = new Map();
  private subscriptions: Map<string, MarketDataSubscription[]> = new Map();
  private tickers: Map<string, Map<string, Ticker>> = new Map(); // symbol -> exchange -> ticker
  private orderBooks: Map<string, Map<string, OrderBook>> = new Map(); // symbol -> exchange -> orderbook
  private trades: Map<string, Trade[]> = new Map(); // symbol -> recent trades
  private isInitialized: boolean = false;
  private updateIntervals: Map<string, NodeJS.Timeout> = new Map();

  // Market data configuration
  private readonly MAX_TRADES_HISTORY = 1000;
  private readonly AGGREGATION_INTERVAL = 1000; // 1 second
  private readonly CACHE_TTL = 5; // 5 seconds

  constructor(config: Config, logger: Logger, cacheManager: CacheManager) {
    super();
    this.config = config;
    this.logger = logger.child({ component: 'market-data' });
    this.cacheManager = cacheManager;
  }

  /**
   * Initialize market data manager and exchange connections
   */
  public async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing market data manager...');

      // Initialize enabled exchanges
      const enabledExchanges = this.config.getEnabledExchanges();

      for (const [name, exchangeConfig] of Object.entries(enabledExchanges)) {
        await this.initializeExchange(name, exchangeConfig);
      }

      // Start aggregation intervals
      this.startAggregationIntervals();

      this.isInitialized = true;
      this.logger.info('Market data manager initialized successfully', {
        exchangeCount: this.exchanges.size
      });

    } catch (error) {
      this.logger.error('Failed to initialize market data manager:', error);
      throw error;
    }
  }

  /**
   * Initialize individual exchange
   */
  private async initializeExchange(name: string, config: any): Promise<void> {
    try {
      let exchange: BaseExchange;

      switch (name) {
        case 'binance':
          exchange = new BinanceExchange(config, this.logger);
          break;
        case 'coinbase':
          exchange = new CoinbaseExchange(config, this.logger);
          break;
        // case 'kraken':
        //   exchange = new KrakenExchange(config, this.logger);
        //   break;
        default:
          this.logger.warn(`Unknown exchange: ${name}`);
          return;
      }

      // Set up event listeners
      exchange.on('ticker', (ticker: Ticker) => this.handleTickerUpdate(name, ticker));
      exchange.on('orderbook', (orderBook: OrderBook) => this.handleOrderBookUpdate(name, orderBook));
      exchange.on('trade', (trade: Trade) => this.handleTradeUpdate(name, trade));
      exchange.on('error', (error: Error) => this.handleExchangeError(name, error));
      exchange.on('connected', () => this.handleExchangeConnected(name));
      exchange.on('disconnected', () => this.handleExchangeDisconnected(name));

      // Initialize exchange
      await exchange.initialize();

      this.exchanges.set(name, exchange);
      this.logger.info(`Exchange ${name} initialized successfully`);

    } catch (error) {
      this.logger.error(`Failed to initialize exchange ${name}:`, error);
      throw error;
    }
  }

  /**
   * Subscribe to market data for symbols
   */
  public async subscribeToSymbols(symbols: string[]): Promise<void> {
    try {
      this.logger.info('Subscribing to market data', { symbols });

      for (const symbol of symbols) {
        // Initialize data structures for symbol
        this.tickers.set(symbol, new Map());
        this.orderBooks.set(symbol, new Map());
        this.trades.set(symbol, []);

        // Subscribe on all exchanges
        for (const [exchangeName, exchange] of this.exchanges) {
          try {
            const subscription: MarketDataSubscription = {
              symbol,
              channels: ['ticker', 'orderbook', 'trades']
            };

            await exchange.subscribeToMarketData([subscription]);
            
            // Store subscription for reconnection purposes
            if (!this.subscriptions.has(exchangeName)) {
              this.subscriptions.set(exchangeName, []);
            }
            this.subscriptions.get(exchangeName)!.push(subscription);

          } catch (error) {
            this.logger.error(`Failed to subscribe to ${symbol} on ${exchangeName}:`, error);
          }
        }
      }

      this.emit('subscribed', symbols);
    } catch (error) {
      this.logger.error('Failed to subscribe to symbols:', error);
      throw error;
    }
  }

  /**
   * Unsubscribe from market data for symbols
   */
  public async unsubscribeFromSymbols(symbols: string[]): Promise<void> {
    try {
      this.logger.info('Unsubscribing from market data', { symbols });

      for (const [exchangeName, exchange] of this.exchanges) {
        try {
          await exchange.unsubscribeFromMarketData(symbols);
          
          // Remove from subscriptions
          const subscriptions = this.subscriptions.get(exchangeName) || [];
          this.subscriptions.set(
            exchangeName,
            subscriptions.filter(sub => !symbols.includes(sub.symbol))
          );

        } catch (error) {
          this.logger.error(`Failed to unsubscribe from ${exchangeName}:`, error);
        }
      }

      // Clean up data structures
      for (const symbol of symbols) {
        this.tickers.delete(symbol);
        this.orderBooks.delete(symbol);
        this.trades.delete(symbol);
      }

      this.emit('unsubscribed', symbols);
    } catch (error) {
      this.logger.error('Failed to unsubscribe from symbols:', error);
      throw error;
    }
  }

  /**
   * Get aggregated ticker for a symbol
   */
  public getAggregatedTicker(symbol: string): AggregatedTicker | null {
    const exchangeTickers = this.tickers.get(symbol);
    if (!exchangeTickers || exchangeTickers.size === 0) {
      return null;
    }

    const exchanges: { [key: string]: any } = {};
    let bestBid = { price: '0', exchange: '' };
    let bestAsk = { price: '999999999', exchange: '' };
    let totalVolume = 0;
    let weightedPriceSum = 0;
    let totalWeight = 0;

    for (const [exchangeName, ticker] of exchangeTickers) {
      exchanges[exchangeName] = {
        bid: ticker.bid,
        ask: ticker.ask,
        last: ticker.last,
        volume: ticker.volume,
        timestamp: ticker.timestamp
      };

      // Find best bid (highest)
      if (parseFloat(ticker.bid) > parseFloat(bestBid.price)) {
        bestBid = { price: ticker.bid, exchange: exchangeName };
      }

      // Find best ask (lowest)
      if (parseFloat(ticker.ask) < parseFloat(bestAsk.price)) {
        bestAsk = { price: ticker.ask, exchange: exchangeName };
      }

      // Calculate weighted price
      const volume = parseFloat(ticker.volume);
      const price = parseFloat(ticker.last);
      totalVolume += volume;
      weightedPriceSum += price * volume;
      totalWeight += volume;
    }

    const weightedPrice = totalWeight > 0 ? (weightedPriceSum / totalWeight).toString() : '0';
    const spread = (parseFloat(bestAsk.price) - parseFloat(bestBid.price)).toString();
    const spreadBps = parseFloat(bestBid.price) > 0 
      ? Math.round((parseFloat(spread) / parseFloat(bestBid.price)) * 10000)
      : 0;

    return {
      symbol,
      exchanges,
      bestBid,
      bestAsk,
      weightedPrice,
      totalVolume: totalVolume.toString(),
      spread,
      spreadBps,
      timestamp: Date.now()
    };
  }

  /**
   * Get aggregated order book for a symbol
   */
  public getAggregatedOrderBook(symbol: string, depth: number = 20): AggregatedOrderBook | null {
    const exchangeOrderBooks = this.orderBooks.get(symbol);
    if (!exchangeOrderBooks || exchangeOrderBooks.size === 0) {
      return null;
    }

    const allBids: Array<{ price: string; size: string; exchange: string; timestamp: number }> = [];
    const allAsks: Array<{ price: string; size: string; exchange: string; timestamp: number }> = [];

    for (const [exchangeName, orderBook] of exchangeOrderBooks) {
      // Add bids
      for (const [price, size] of orderBook.bids.slice(0, depth)) {
        allBids.push({
          price,
          size,
          exchange: exchangeName,
          timestamp: orderBook.timestamp
        });
      }

      // Add asks
      for (const [price, size] of orderBook.asks.slice(0, depth)) {
        allAsks.push({
          price,
          size,
          exchange: exchangeName,
          timestamp: orderBook.timestamp
        });
      }
    }

    // Sort bids (highest price first)
    allBids.sort((a, b) => parseFloat(b.price) - parseFloat(a.price));
    
    // Sort asks (lowest price first)
    allAsks.sort((a, b) => parseFloat(a.price) - parseFloat(b.price));

    // Take top levels
    const bids = allBids.slice(0, depth);
    const asks = allAsks.slice(0, depth);

    const bestBid = bids.length > 0 ? bids[0].price : '0';
    const bestAsk = asks.length > 0 ? asks[0].price : '999999999';
    const spread = (parseFloat(bestAsk) - parseFloat(bestBid)).toString();

    // Calculate depth
    const bidDepth = bids.reduce((sum, bid) => sum + parseFloat(bid.size), 0).toString();
    const askDepth = asks.reduce((sum, ask) => sum + parseFloat(ask.size), 0).toString();

    return {
      symbol,
      bids,
      asks,
      bestBid,
      bestAsk,
      spread,
      depth: {
        bids: bidDepth,
        asks: askDepth
      },
      timestamp: Date.now()
    };
  }

  /**
   * Get recent trades for a symbol
   */
  public getRecentTrades(symbol: string, limit: number = 100): Trade[] {
    const trades = this.trades.get(symbol) || [];
    return trades.slice(-limit);
  }

  /**
   * Get market statistics for a symbol
   */
  public getMarketStats(symbol: string): MarketStats | null {
    const ticker = this.getAggregatedTicker(symbol);
    if (!ticker) {
      return null;
    }

    const trades = this.getRecentTrades(symbol, 1000);
    const prices = trades.map(t => parseFloat(t.price));
    
    let high24h = '0';
    let low24h = '999999999';
    let volume24h = '0';
    
    if (prices.length > 0) {
      high24h = Math.max(...prices).toString();
      low24h = Math.min(...prices).toString();
      volume24h = trades.reduce((sum, t) => sum + parseFloat(t.amount), 0).toString();
    }

    // Calculate volatility (standard deviation of price changes)
    let volatility = '0';
    if (prices.length > 1) {
      const returns = prices.slice(1).map((price, i) => 
        (price - prices[i]) / prices[i]
      );
      const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
      const variance = returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length;
      volatility = Math.sqrt(variance).toString();
    }

    return {
      symbol,
      price24hChange: '0', // Would need historical data
      price24hChangePercent: '0',
      high24h,
      low24h,
      volume24h,
      volatility,
      averageSpread: ticker.spread,
      exchangeCount: Object.keys(ticker.exchanges).length,
      lastUpdate: ticker.timestamp
    };
  }

  /**
   * Handle ticker update from exchange
   */
  private async handleTickerUpdate(exchangeName: string, ticker: Ticker): Promise<void> {
    const exchangeTickers = this.tickers.get(ticker.symbol);
    if (exchangeTickers) {
      exchangeTickers.set(exchangeName, ticker);
      
      // Cache individual ticker
      await this.cacheManager.setTicker(exchangeName, ticker.symbol, ticker);
      
      this.emit('ticker', exchangeName, ticker);
    }
  }

  /**
   * Handle order book update from exchange
   */
  private async handleOrderBookUpdate(exchangeName: string, orderBook: OrderBook): Promise<void> {
    const exchangeOrderBooks = this.orderBooks.get(orderBook.symbol);
    if (exchangeOrderBooks) {
      exchangeOrderBooks.set(exchangeName, orderBook);
      
      // Cache individual order book
      await this.cacheManager.setOrderBook(exchangeName, orderBook.symbol, orderBook);
      
      this.emit('orderbook', exchangeName, orderBook);
    }
  }

  /**
   * Handle trade update from exchange
   */
  private async handleTradeUpdate(exchangeName: string, trade: Trade): Promise<void> {
    const trades = this.trades.get(trade.symbol);
    if (trades) {
      trades.push(trade);
      
      // Keep only recent trades
      if (trades.length > this.MAX_TRADES_HISTORY) {
        trades.splice(0, trades.length - this.MAX_TRADES_HISTORY);
      }
      
      this.emit('trade', exchangeName, trade);
    }
  }

  /**
   * Handle exchange error
   */
  private handleExchangeError(exchangeName: string, error: Error): void {
    this.logger.error(`Exchange ${exchangeName} error:`, error);
    this.emit('exchangeError', exchangeName, error);
  }

  /**
   * Handle exchange connected
   */
  private handleExchangeConnected(exchangeName: string): void {
    this.logger.info(`Exchange ${exchangeName} connected`);
    this.emit('exchangeConnected', exchangeName);
  }

  /**
   * Handle exchange disconnected
   */
  private handleExchangeDisconnected(exchangeName: string): void {
    this.logger.warn(`Exchange ${exchangeName} disconnected`);
    this.emit('exchangeDisconnected', exchangeName);
  }

  /**
   * Start aggregation intervals
   */
  private startAggregationIntervals(): void {
    // Aggregate and cache market data every second
    const aggregationInterval = setInterval(async () => {
      await this.aggregateAndCacheData();
    }, this.AGGREGATION_INTERVAL);

    this.updateIntervals.set('aggregation', aggregationInterval);
  }

  /**
   * Aggregate and cache market data
   */
  private async aggregateAndCacheData(): Promise<void> {
    try {
      for (const symbol of this.tickers.keys()) {
        // Cache aggregated ticker
        const aggregatedTicker = this.getAggregatedTicker(symbol);
        if (aggregatedTicker) {
          await this.cacheManager.setMarketData(
            `aggregated_ticker:${symbol}`,
            aggregatedTicker,
            this.CACHE_TTL
          );
        }

        // Cache aggregated order book
        const aggregatedOrderBook = this.getAggregatedOrderBook(symbol);
        if (aggregatedOrderBook) {
          await this.cacheManager.setMarketData(
            `aggregated_orderbook:${symbol}`,
            aggregatedOrderBook,
            this.CACHE_TTL
          );
        }

        // Cache market stats
        const marketStats = this.getMarketStats(symbol);
        if (marketStats) {
          await this.cacheManager.setMarketData(
            `market_stats:${symbol}`,
            marketStats,
            60 // 1 minute TTL for stats
          );
        }
      }
    } catch (error) {
      this.logger.error('Failed to aggregate and cache data:', error);
    }
  }

  /**
   * Get exchange instance
   */
  public getExchange(name: string): BaseExchange | undefined {
    return this.exchanges.get(name);
  }

  /**
   * Get all connected exchanges
   */
  public getConnectedExchanges(): string[] {
    return Array.from(this.exchanges.keys()).filter(name => {
      const exchange = this.exchanges.get(name);
      return exchange?.isExchangeConnected();
    });
  }

  /**
   * Health check
   */
  public async healthCheck(): Promise<{ status: 'healthy' | 'degraded' | 'unhealthy'; exchanges: any }> {
    const exchangeStatus: any = {};
    let healthyCount = 0;

    for (const [name, exchange] of this.exchanges) {
      const isConnected = exchange.isExchangeConnected();
      exchangeStatus[name] = {
        connected: isConnected,
        lastUpdate: Date.now() // Would track actual last update time
      };
      
      if (isConnected) {
        healthyCount++;
      }
    }

    const totalExchanges = this.exchanges.size;
    let status: 'healthy' | 'degraded' | 'unhealthy';

    if (healthyCount === totalExchanges) {
      status = 'healthy';
    } else if (healthyCount > 0) {
      status = 'degraded';
    } else {
      status = 'unhealthy';
    }

    return { status, exchanges: exchangeStatus };
  }

  /**
   * Close all exchange connections
   */
  public async close(): Promise<void> {
    // Clear intervals
    for (const interval of this.updateIntervals.values()) {
      clearInterval(interval);
    }
    this.updateIntervals.clear();

    // Close all exchanges
    for (const exchange of this.exchanges.values()) {
      await exchange.close();
    }

    this.exchanges.clear();
    this.logger.info('Market data manager closed');
  }

  /**
   * Check if manager is ready
   */
  public isReady(): boolean {
    return this.isInitialized && this.exchanges.size > 0;
  }
}
