/**
 * Advanced Rate Limiting System for AlphaMarketMaker API
 * 
 * Configurable rate limiting with Redis backend, user-specific limits,
 * endpoint-specific rules, and intelligent throttling mechanisms.
 * 
 * Git Commit: "feat: add advanced RateLimiter with Redis backend and user-specific limits"
 */

import { Request, Response, NextFunction } from 'express';
import { Logger } from '@/utils/Logger';
import { CacheManager } from '@/data/CacheManager';

export interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  skipSuccessfulRequests?: boolean; // Don't count successful requests
  skipFailedRequests?: boolean; // Don't count failed requests
  keyGenerator?: (req: Request) => string; // Custom key generator
  onLimitReached?: (req: Request, res: Response) => void; // Custom limit handler
  message?: string; // Custom error message
  headers?: boolean; // Include rate limit headers
}

export interface EndpointLimits {
  [endpoint: string]: RateLimitConfig;
}

export interface UserTier {
  name: string;
  multiplier: number; // Rate limit multiplier (higher = more requests allowed)
  dailyLimit: number; // Daily request limit
  burstLimit: number; // Burst request limit
}

export interface RateLimitInfo {
  limit: number;
  remaining: number;
  resetTime: number;
  retryAfter?: number;
}

export interface RateLimitResult {
  allowed: boolean;
  info: RateLimitInfo;
  error?: string;
}

export class RateLimiter {
  private logger: Logger;
  private cacheManager: CacheManager;
  private defaultConfig: RateLimitConfig;
  private endpointLimits: EndpointLimits = {};
  private userTiers: Map<string, UserTier> = new Map();

  // Default user tiers
  private readonly DEFAULT_TIERS: UserTier[] = [
    { name: 'free', multiplier: 1, dailyLimit: 1000, burstLimit: 10 },
    { name: 'basic', multiplier: 2, dailyLimit: 5000, burstLimit: 20 },
    { name: 'premium', multiplier: 5, dailyLimit: 25000, burstLimit: 50 },
    { name: 'enterprise', multiplier: 10, dailyLimit: 100000, burstLimit: 100 }
  ];

  constructor(logger: Logger, cacheManager: CacheManager, defaultConfig?: RateLimitConfig) {
    this.logger = logger.child({ component: 'rate-limiter' });
    this.cacheManager = cacheManager;
    
    this.defaultConfig = defaultConfig || {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 100,
      headers: true,
      message: 'Too many requests, please try again later.'
    };

    this.initializeUserTiers();
    this.setupEndpointLimits();
  }

  /**
   * Initialize user tiers
   */
  private initializeUserTiers(): void {
    for (const tier of this.DEFAULT_TIERS) {
      this.userTiers.set(tier.name, tier);
    }
  }

  /**
   * Setup endpoint-specific rate limits
   */
  private setupEndpointLimits(): void {
    this.endpointLimits = {
      // Authentication endpoints - stricter limits
      '/api/v1/auth/login': {
        windowMs: 15 * 60 * 1000, // 15 minutes
        maxRequests: 5,
        message: 'Too many login attempts, please try again later.'
      },
      '/api/v1/auth/register': {
        windowMs: 60 * 60 * 1000, // 1 hour
        maxRequests: 3,
        message: 'Too many registration attempts, please try again later.'
      },

      // Trading endpoints - moderate limits
      '/api/v1/start': {
        windowMs: 1 * 60 * 1000, // 1 minute
        maxRequests: 10,
        message: 'Trading start rate limit exceeded.'
      },
      '/api/v1/stop': {
        windowMs: 1 * 60 * 1000, // 1 minute
        maxRequests: 10,
        message: 'Trading stop rate limit exceeded.'
      },
      '/api/v1/orders': {
        windowMs: 1 * 60 * 1000, // 1 minute
        maxRequests: 100,
        message: 'Order placement rate limit exceeded.'
      },

      // Data endpoints - generous limits
      '/api/v1/portfolio': {
        windowMs: 1 * 60 * 1000, // 1 minute
        maxRequests: 60,
        message: 'Portfolio data rate limit exceeded.'
      },
      '/api/v1/risk': {
        windowMs: 1 * 60 * 1000, // 1 minute
        maxRequests: 60,
        message: 'Risk data rate limit exceeded.'
      },
      '/api/v1/health': {
        windowMs: 1 * 60 * 1000, // 1 minute
        maxRequests: 120,
        message: 'Health check rate limit exceeded.'
      }
    };
  }

  /**
   * Create rate limiting middleware
   */
  public createMiddleware(config?: RateLimitConfig): (req: Request, res: Response, next: NextFunction) => Promise<void> {
    const limitConfig = { ...this.defaultConfig, ...config };

    return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
      try {
        const result = await this.checkRateLimit(req, limitConfig);

        // Add rate limit headers
        if (limitConfig.headers) {
          res.set({
            'X-RateLimit-Limit': result.info.limit.toString(),
            'X-RateLimit-Remaining': result.info.remaining.toString(),
            'X-RateLimit-Reset': new Date(result.info.resetTime).toISOString()
          });

          if (result.info.retryAfter) {
            res.set('Retry-After', Math.ceil(result.info.retryAfter / 1000).toString());
          }
        }

        if (!result.allowed) {
          this.logger.warn('Rate limit exceeded', {
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            endpoint: req.path,
            method: req.method,
            limit: result.info.limit,
            resetTime: result.info.resetTime
          });

          res.status(429).json({
            error: 'Rate limit exceeded',
            message: limitConfig.message || result.error,
            retryAfter: result.info.retryAfter ? Math.ceil(result.info.retryAfter / 1000) : undefined,
            limit: result.info.limit,
            remaining: result.info.remaining,
            resetTime: result.info.resetTime
          });
          return;
        }

        next();
      } catch (error) {
        this.logger.error('Rate limiting error:', error);
        // Fail open - allow request if rate limiter fails
        next();
      }
    };
  }

  /**
   * Create endpoint-specific middleware
   */
  public createEndpointMiddleware(endpoint: string): (req: Request, res: Response, next: NextFunction) => Promise<void> {
    const config = this.endpointLimits[endpoint] || this.defaultConfig;
    return this.createMiddleware(config);
  }

  /**
   * Check rate limit for a request
   */
  public async checkRateLimit(req: Request, config: RateLimitConfig): Promise<RateLimitResult> {
    try {
      // Generate cache key
      const key = this.generateKey(req, config);
      
      // Get user tier for limit calculation
      const userTier = this.getUserTier(req);
      const effectiveLimit = Math.floor(config.maxRequests * userTier.multiplier);

      // Check rate limit using sliding window
      const result = await this.cacheManager.checkRateLimit(
        key,
        effectiveLimit,
        Math.floor(config.windowMs / 1000)
      );

      const info: RateLimitInfo = {
        limit: effectiveLimit,
        remaining: result.remaining,
        resetTime: result.resetTime
      };

      if (!result.allowed) {
        info.retryAfter = result.resetTime - Date.now();
      }

      return {
        allowed: result.allowed,
        info,
        error: result.allowed ? undefined : config.message
      };

    } catch (error) {
      this.logger.error('Rate limit check failed:', error);
      
      // Fail open with default limits
      return {
        allowed: true,
        info: {
          limit: config.maxRequests,
          remaining: config.maxRequests - 1,
          resetTime: Date.now() + config.windowMs
        }
      };
    }
  }

  /**
   * Generate cache key for rate limiting
   */
  private generateKey(req: Request, config: RateLimitConfig): string {
    if (config.keyGenerator) {
      return config.keyGenerator(req);
    }

    // Default key generation strategy
    const userId = this.getUserId(req);
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    const endpoint = req.path;
    const method = req.method;

    // Use user ID if available, otherwise fall back to IP
    const identifier = userId || ip;
    
    return `ratelimit:${identifier}:${method}:${endpoint}`;
  }

  /**
   * Get user ID from request
   */
  private getUserId(req: Request): string | null {
    // Check JWT token
    const user = (req as any).user;
    if (user && user.id) {
      return user.id;
    }

    // Check API key
    const apiKey = req.headers['x-api-key'] as string;
    if (apiKey) {
      return `api:${apiKey}`;
    }

    return null;
  }

  /**
   * Get user tier for rate limit calculation
   */
  private getUserTier(req: Request): UserTier {
    const user = (req as any).user;
    
    if (user && user.tier) {
      const tier = this.userTiers.get(user.tier);
      if (tier) {
        return tier;
      }
    }

    // Default to free tier
    return this.userTiers.get('free')!;
  }

  /**
   * Add custom user tier
   */
  public addUserTier(tier: UserTier): void {
    this.userTiers.set(tier.name, tier);
    this.logger.info(`Added user tier: ${tier.name}`, {
      multiplier: tier.multiplier,
      dailyLimit: tier.dailyLimit,
      burstLimit: tier.burstLimit
    });
  }

  /**
   * Update endpoint limits
   */
  public updateEndpointLimits(endpoint: string, config: RateLimitConfig): void {
    this.endpointLimits[endpoint] = config;
    this.logger.info(`Updated rate limits for endpoint: ${endpoint}`, {
      windowMs: config.windowMs,
      maxRequests: config.maxRequests
    });
  }

  /**
   * Get current rate limit status for a request
   */
  public async getRateLimitStatus(req: Request): Promise<RateLimitInfo[]> {
    const statuses: RateLimitInfo[] = [];

    try {
      // Check general rate limit
      const generalResult = await this.checkRateLimit(req, this.defaultConfig);
      statuses.push(generalResult.info);

      // Check endpoint-specific rate limit
      const endpointConfig = this.endpointLimits[req.path];
      if (endpointConfig) {
        const endpointResult = await this.checkRateLimit(req, endpointConfig);
        statuses.push(endpointResult.info);
      }

      return statuses;
    } catch (error) {
      this.logger.error('Failed to get rate limit status:', error);
      return [];
    }
  }

  /**
   * Reset rate limit for a key
   */
  public async resetRateLimit(key: string): Promise<boolean> {
    try {
      await this.cacheManager.delete(`ratelimit:${key}`);
      this.logger.info(`Rate limit reset for key: ${key}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to reset rate limit for key ${key}:`, error);
      return false;
    }
  }

  /**
   * Get rate limit statistics
   */
  public async getStatistics(): Promise<{
    totalRequests: number;
    blockedRequests: number;
    topEndpoints: Array<{ endpoint: string; requests: number }>;
    topUsers: Array<{ user: string; requests: number }>;
  }> {
    try {
      // This would require additional tracking in a real implementation
      // For now, return placeholder data
      return {
        totalRequests: 0,
        blockedRequests: 0,
        topEndpoints: [],
        topUsers: []
      };
    } catch (error) {
      this.logger.error('Failed to get rate limit statistics:', error);
      return {
        totalRequests: 0,
        blockedRequests: 0,
        topEndpoints: [],
        topUsers: []
      };
    }
  }

  /**
   * Create burst protection middleware
   */
  public createBurstProtection(maxBurst: number = 10, windowMs: number = 1000): (req: Request, res: Response, next: NextFunction) => Promise<void> {
    return this.createMiddleware({
      windowMs,
      maxRequests: maxBurst,
      message: 'Burst limit exceeded, please slow down your requests.'
    });
  }

  /**
   * Create IP-based rate limiter
   */
  public createIPLimiter(config: RateLimitConfig): (req: Request, res: Response, next: NextFunction) => Promise<void> {
    const ipConfig = {
      ...config,
      keyGenerator: (req: Request) => `ip:${req.ip || req.connection.remoteAddress || 'unknown'}`
    };

    return this.createMiddleware(ipConfig);
  }

  /**
   * Create user-based rate limiter
   */
  public createUserLimiter(config: RateLimitConfig): (req: Request, res: Response, next: NextFunction) => Promise<void> {
    const userConfig = {
      ...config,
      keyGenerator: (req: Request) => {
        const userId = this.getUserId(req);
        return userId ? `user:${userId}` : `ip:${req.ip || 'unknown'}`;
      }
    };

    return this.createMiddleware(userConfig);
  }

  /**
   * Health check for rate limiter
   */
  public async healthCheck(): Promise<{ status: 'healthy' | 'unhealthy'; latency: number }> {
    try {
      const start = Date.now();
      
      // Test cache connectivity
      const testKey = 'ratelimit:healthcheck';
      await this.cacheManager.set(testKey, 'test', { ttl: 1 });
      await this.cacheManager.get(testKey);
      await this.cacheManager.delete(testKey);
      
      const latency = Date.now() - start;
      
      return { status: 'healthy', latency };
    } catch (error) {
      this.logger.error('Rate limiter health check failed:', error);
      return { status: 'unhealthy', latency: -1 };
    }
  }
}
