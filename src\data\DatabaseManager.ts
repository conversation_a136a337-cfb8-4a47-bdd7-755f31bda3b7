/**
 * Database Management for AlphaMarketMaker
 * 
 * Handles database connections, migrations, and provides a unified interface
 * for data persistence across PostgreSQL and SQLite environments.
 */

import knex, { Knex } from 'knex';
import { Config } from '@/utils/Config';
import { Logger } from '@/utils/Logger';

export interface TradeRecord {
  id?: string;
  exchange: string;
  symbol: string;
  side: 'buy' | 'sell';
  amount: string;
  price: string;
  fee: string;
  orderId: string;
  tradeId: string;
  timestamp: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface OrderRecord {
  id?: string;
  exchange: string;
  symbol: string;
  side: 'buy' | 'sell';
  type: string;
  amount: string;
  price: string;
  filled: string;
  remaining: string;
  status: string;
  orderId: string;
  clientOrderId?: string;
  timestamp: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface PositionRecord {
  id?: string;
  exchange: string;
  symbol: string;
  size: string;
  entryPrice: string;
  markPrice: string;
  unrealizedPnl: string;
  realizedPnl: string;
  timestamp: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface BalanceRecord {
  id?: string;
  exchange: string;
  currency: string;
  total: string;
  available: string;
  locked: string;
  timestamp: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

export class DatabaseManager {
  private db: Knex;
  private config: Config;
  private logger: Logger;
  private isInitialized: boolean = false;

  constructor(config: Config, logger: Logger) {
    this.config = config;
    this.logger = logger.child({ component: 'database' });
  }

  /**
   * Initialize database connection and run migrations
   */
  public async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing database connection...');

      const dbConfig = this.config.getDatabase();
      
      // Configure Knex based on database type
      const knexConfig: Knex.Config = {
        client: dbConfig.type === 'postgresql' ? 'pg' : 'sqlite3',
        connection: dbConfig.type === 'postgresql' 
          ? dbConfig.url
          : { filename: dbConfig.url.replace('sqlite:', '') },
        useNullAsDefault: dbConfig.type === 'sqlite',
        migrations: {
          directory: './migrations',
          extension: 'ts'
        },
        seeds: {
          directory: './seeds'
        }
      };

      // Add connection pool for PostgreSQL
      if (dbConfig.type === 'postgresql' && dbConfig.pool) {
        knexConfig.pool = dbConfig.pool;
      }

      this.db = knex(knexConfig);

      // Test connection
      await this.testConnection();

      // Run migrations
      await this.runMigrations();

      this.isInitialized = true;
      this.logger.info('Database initialized successfully');

    } catch (error) {
      this.logger.error('Failed to initialize database:', error);
      throw error;
    }
  }

  /**
   * Test database connection
   */
  private async testConnection(): Promise<void> {
    try {
      await this.db.raw('SELECT 1');
      this.logger.info('Database connection test successful');
    } catch (error) {
      this.logger.error('Database connection test failed:', error);
      throw new Error('Database connection failed');
    }
  }

  /**
   * Run database migrations
   */
  private async runMigrations(): Promise<void> {
    try {
      this.logger.info('Running database migrations...');
      const [batchNo, migrations] = await this.db.migrate.latest();
      
      if (migrations.length === 0) {
        this.logger.info('Database is up to date');
      } else {
        this.logger.info(`Ran ${migrations.length} migrations in batch ${batchNo}`);
      }
    } catch (error) {
      this.logger.error('Migration failed:', error);
      throw error;
    }
  }

  /**
   * Create database tables if they don't exist
   */
  public async createTables(): Promise<void> {
    try {
      // Trades table
      if (!(await this.db.schema.hasTable('trades'))) {
        await this.db.schema.createTable('trades', (table) => {
          table.uuid('id').primary().defaultTo(this.db.raw('gen_random_uuid()'));
          table.string('exchange').notNullable();
          table.string('symbol').notNullable();
          table.enum('side', ['buy', 'sell']).notNullable();
          table.decimal('amount', 20, 8).notNullable();
          table.decimal('price', 20, 8).notNullable();
          table.decimal('fee', 20, 8).defaultTo(0);
          table.string('orderId').notNullable();
          table.string('tradeId').notNullable();
          table.timestamp('timestamp').notNullable();
          table.timestamps(true, true);
          
          table.index(['exchange', 'symbol']);
          table.index(['timestamp']);
          table.index(['orderId']);
        });
      }

      // Orders table
      if (!(await this.db.schema.hasTable('orders'))) {
        await this.db.schema.createTable('orders', (table) => {
          table.uuid('id').primary().defaultTo(this.db.raw('gen_random_uuid()'));
          table.string('exchange').notNullable();
          table.string('symbol').notNullable();
          table.enum('side', ['buy', 'sell']).notNullable();
          table.string('type').notNullable();
          table.decimal('amount', 20, 8).notNullable();
          table.decimal('price', 20, 8).notNullable();
          table.decimal('filled', 20, 8).defaultTo(0);
          table.decimal('remaining', 20, 8).notNullable();
          table.string('status').notNullable();
          table.string('orderId').notNullable();
          table.string('clientOrderId').nullable();
          table.timestamp('timestamp').notNullable();
          table.timestamps(true, true);
          
          table.index(['exchange', 'symbol']);
          table.index(['status']);
          table.index(['orderId']);
          table.index(['timestamp']);
        });
      }

      // Positions table
      if (!(await this.db.schema.hasTable('positions'))) {
        await this.db.schema.createTable('positions', (table) => {
          table.uuid('id').primary().defaultTo(this.db.raw('gen_random_uuid()'));
          table.string('exchange').notNullable();
          table.string('symbol').notNullable();
          table.decimal('size', 20, 8).notNullable();
          table.decimal('entryPrice', 20, 8).notNullable();
          table.decimal('markPrice', 20, 8).notNullable();
          table.decimal('unrealizedPnl', 20, 8).defaultTo(0);
          table.decimal('realizedPnl', 20, 8).defaultTo(0);
          table.timestamp('timestamp').notNullable();
          table.timestamps(true, true);
          
          table.unique(['exchange', 'symbol']);
          table.index(['timestamp']);
        });
      }

      // Balances table
      if (!(await this.db.schema.hasTable('balances'))) {
        await this.db.schema.createTable('balances', (table) => {
          table.uuid('id').primary().defaultTo(this.db.raw('gen_random_uuid()'));
          table.string('exchange').notNullable();
          table.string('currency').notNullable();
          table.decimal('total', 20, 8).notNullable();
          table.decimal('available', 20, 8).notNullable();
          table.decimal('locked', 20, 8).defaultTo(0);
          table.timestamp('timestamp').notNullable();
          table.timestamps(true, true);
          
          table.unique(['exchange', 'currency']);
          table.index(['timestamp']);
        });
      }

      this.logger.info('Database tables created successfully');
    } catch (error) {
      this.logger.error('Failed to create tables:', error);
      throw error;
    }
  }

  /**
   * Insert trade record
   */
  public async insertTrade(trade: TradeRecord): Promise<string> {
    try {
      const [result] = await this.db('trades').insert(trade).returning('id');
      this.logger.debug('Trade record inserted', { tradeId: result.id });
      return result.id;
    } catch (error) {
      this.logger.error('Failed to insert trade:', error);
      throw error;
    }
  }

  /**
   * Insert order record
   */
  public async insertOrder(order: OrderRecord): Promise<string> {
    try {
      const [result] = await this.db('orders').insert(order).returning('id');
      this.logger.debug('Order record inserted', { orderId: result.id });
      return result.id;
    } catch (error) {
      this.logger.error('Failed to insert order:', error);
      throw error;
    }
  }

  /**
   * Update order record
   */
  public async updateOrder(orderId: string, updates: Partial<OrderRecord>): Promise<void> {
    try {
      await this.db('orders')
        .where('orderId', orderId)
        .update({ ...updates, updatedAt: new Date() });
      
      this.logger.debug('Order record updated', { orderId });
    } catch (error) {
      this.logger.error('Failed to update order:', error);
      throw error;
    }
  }

  /**
   * Upsert position record
   */
  public async upsertPosition(position: PositionRecord): Promise<void> {
    try {
      await this.db('positions')
        .insert(position)
        .onConflict(['exchange', 'symbol'])
        .merge(['size', 'entryPrice', 'markPrice', 'unrealizedPnl', 'realizedPnl', 'timestamp', 'updatedAt']);
      
      this.logger.debug('Position record upserted', { 
        exchange: position.exchange, 
        symbol: position.symbol 
      });
    } catch (error) {
      this.logger.error('Failed to upsert position:', error);
      throw error;
    }
  }

  /**
   * Upsert balance record
   */
  public async upsertBalance(balance: BalanceRecord): Promise<void> {
    try {
      await this.db('balances')
        .insert(balance)
        .onConflict(['exchange', 'currency'])
        .merge(['total', 'available', 'locked', 'timestamp', 'updatedAt']);
      
      this.logger.debug('Balance record upserted', { 
        exchange: balance.exchange, 
        currency: balance.currency 
      });
    } catch (error) {
      this.logger.error('Failed to upsert balance:', error);
      throw error;
    }
  }

  /**
   * Get trades with pagination
   */
  public async getTrades(
    filters: {
      exchange?: string;
      symbol?: string;
      startDate?: Date;
      endDate?: Date;
    } = {},
    page: number = 1,
    limit: number = 100
  ): Promise<{ trades: TradeRecord[]; total: number }> {
    try {
      let query = this.db('trades');

      // Apply filters
      if (filters.exchange) query = query.where('exchange', filters.exchange);
      if (filters.symbol) query = query.where('symbol', filters.symbol);
      if (filters.startDate) query = query.where('timestamp', '>=', filters.startDate);
      if (filters.endDate) query = query.where('timestamp', '<=', filters.endDate);

      // Get total count
      const [{ count }] = await query.clone().count('* as count');
      const total = parseInt(count as string, 10);

      // Get paginated results
      const trades = await query
        .orderBy('timestamp', 'desc')
        .limit(limit)
        .offset((page - 1) * limit);

      return { trades, total };
    } catch (error) {
      this.logger.error('Failed to get trades:', error);
      throw error;
    }
  }

  /**
   * Get current positions
   */
  public async getPositions(exchange?: string): Promise<PositionRecord[]> {
    try {
      let query = this.db('positions');
      
      if (exchange) {
        query = query.where('exchange', exchange);
      }

      const positions = await query.orderBy('updatedAt', 'desc');
      return positions;
    } catch (error) {
      this.logger.error('Failed to get positions:', error);
      throw error;
    }
  }

  /**
   * Get current balances
   */
  public async getBalances(exchange?: string): Promise<BalanceRecord[]> {
    try {
      let query = this.db('balances');
      
      if (exchange) {
        query = query.where('exchange', exchange);
      }

      const balances = await query.orderBy('updatedAt', 'desc');
      return balances;
    } catch (error) {
      this.logger.error('Failed to get balances:', error);
      throw error;
    }
  }

  /**
   * Get open orders
   */
  public async getOpenOrders(exchange?: string, symbol?: string): Promise<OrderRecord[]> {
    try {
      let query = this.db('orders').where('status', 'open');
      
      if (exchange) query = query.where('exchange', exchange);
      if (symbol) query = query.where('symbol', symbol);

      const orders = await query.orderBy('timestamp', 'desc');
      return orders;
    } catch (error) {
      this.logger.error('Failed to get open orders:', error);
      throw error;
    }
  }

  /**
   * Execute raw SQL query
   */
  public async raw(sql: string, bindings?: any[]): Promise<any> {
    try {
      return await this.db.raw(sql, bindings);
    } catch (error) {
      this.logger.error('Raw query failed:', error);
      throw error;
    }
  }

  /**
   * Start database transaction
   */
  public async transaction<T>(callback: (trx: Knex.Transaction) => Promise<T>): Promise<T> {
    return await this.db.transaction(callback);
  }

  /**
   * Close database connection
   */
  public async close(): Promise<void> {
    if (this.db) {
      await this.db.destroy();
      this.logger.info('Database connection closed');
    }
  }

  /**
   * Check if database is initialized
   */
  public isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * Get database instance (for advanced queries)
   */
  public getKnex(): Knex {
    return this.db;
  }
}
