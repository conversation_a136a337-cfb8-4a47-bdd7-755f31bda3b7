/**
 * Structured Logging Utility for AlphaMarketMaker
 * 
 * Provides comprehensive logging with correlation IDs, structured JSON output,
 * and integration with monitoring systems for production environments.
 */

import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import { v4 as uuidv4 } from 'uuid';

export interface LogContext {
  correlationId?: string;
  userId?: string;
  exchange?: string;
  symbol?: string;
  orderId?: string;
  tradeId?: string;
  component?: string;
  method?: string;
  duration?: number;
  [key: string]: any;
}

export interface LoggerConfig {
  level: string;
  service: string;
  environment: string;
  enableFile?: boolean;
  filename?: string;
  maxSize?: string;
  maxFiles?: number;
}

export class Logger {
  private winston: winston.Logger;
  private config: LoggerConfig;
  private correlationId: string;

  constructor(config: LoggerConfig) {
    this.config = config;
    this.correlationId = uuidv4();
    this.initializeWinston();
  }

  /**
   * Initialize Winston logger with appropriate transports
   */
  private initializeWinston(): void {
    const transports: winston.transport[] = [];

    // Console transport with colored output for development
    const consoleFormat = this.config.environment === 'development'
      ? winston.format.combine(
          winston.format.colorize(),
          winston.format.timestamp(),
          winston.format.printf(({ timestamp, level, message, ...meta }) => {
            const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
            return `${timestamp} [${level}]: ${message} ${metaStr}`;
          })
        )
      : winston.format.combine(
          winston.format.timestamp(),
          winston.format.json()
        );

    transports.push(
      new winston.transports.Console({
        format: consoleFormat,
        level: this.config.level
      })
    );

    // File transport with daily rotation
    if (this.config.enableFile) {
      transports.push(
        new DailyRotateFile({
          filename: this.config.filename || 'logs/alphamarketmaker-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          maxSize: this.config.maxSize || '20m',
          maxFiles: this.config.maxFiles || 14,
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json()
          ),
          level: this.config.level
        })
      );

      // Error log file
      transports.push(
        new DailyRotateFile({
          filename: 'logs/error-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          maxSize: this.config.maxSize || '20m',
          maxFiles: this.config.maxFiles || 14,
          level: 'error',
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json()
          )
        })
      );
    }

    this.winston = winston.createLogger({
      level: this.config.level,
      defaultMeta: {
        service: this.config.service,
        environment: this.config.environment,
        correlationId: this.correlationId
      },
      transports,
      exitOnError: false
    });

    // Handle uncaught exceptions and unhandled rejections
    this.winston.exceptions.handle(
      new winston.transports.File({ filename: 'logs/exceptions.log' })
    );

    this.winston.rejections.handle(
      new winston.transports.File({ filename: 'logs/rejections.log' })
    );
  }

  /**
   * Create a child logger with additional context
   */
  public child(context: LogContext): Logger {
    const childLogger = new Logger(this.config);
    childLogger.winston = this.winston.child(context);
    return childLogger;
  }

  /**
   * Set correlation ID for request tracking
   */
  public setCorrelationId(correlationId: string): void {
    this.correlationId = correlationId;
    this.winston.defaultMeta = {
      ...this.winston.defaultMeta,
      correlationId
    };
  }

  /**
   * Log debug message
   */
  public debug(message: string, context?: LogContext): void {
    this.winston.debug(message, context);
  }

  /**
   * Log info message
   */
  public info(message: string, context?: LogContext): void {
    this.winston.info(message, context);
  }

  /**
   * Log warning message
   */
  public warn(message: string, context?: LogContext): void {
    this.winston.warn(message, context);
  }

  /**
   * Log error message
   */
  public error(message: string, error?: Error | any, context?: LogContext): void {
    const errorContext = {
      ...context,
      error: error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack
      } : error
    };
    
    this.winston.error(message, errorContext);
  }

  /**
   * Log trading activity
   */
  public trade(message: string, context: {
    exchange: string;
    symbol: string;
    side: 'buy' | 'sell';
    amount: number;
    price: number;
    orderId?: string;
    tradeId?: string;
  }): void {
    this.winston.info(message, {
      ...context,
      component: 'trading',
      type: 'trade'
    });
  }

  /**
   * Log order activity
   */
  public order(message: string, context: {
    exchange: string;
    symbol: string;
    side: 'buy' | 'sell';
    amount: number;
    price: number;
    orderId: string;
    status: string;
    type?: string;
  }): void {
    this.winston.info(message, {
      ...context,
      component: 'orders',
      type: 'order'
    });
  }

  /**
   * Log market data updates
   */
  public market(message: string, context: {
    exchange: string;
    symbol: string;
    price?: number;
    volume?: number;
    spread?: number;
    timestamp?: number;
  }): void {
    this.winston.debug(message, {
      ...context,
      component: 'market-data',
      type: 'market'
    });
  }

  /**
   * Log risk management events
   */
  public risk(message: string, context: {
    symbol?: string;
    position?: number;
    exposure?: number;
    limit?: number;
    action?: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
  }): void {
    const level = context.severity === 'critical' ? 'error' : 
                  context.severity === 'high' ? 'warn' : 'info';
    
    this.winston.log(level, message, {
      ...context,
      component: 'risk-management',
      type: 'risk'
    });
  }

  /**
   * Log performance metrics
   */
  public performance(message: string, context: {
    component: string;
    method: string;
    duration: number;
    success: boolean;
    metadata?: any;
  }): void {
    this.winston.info(message, {
      ...context,
      type: 'performance'
    });
  }

  /**
   * Log API requests
   */
  public api(message: string, context: {
    method: string;
    url: string;
    statusCode: number;
    duration: number;
    userAgent?: string;
    ip?: string;
    userId?: string;
  }): void {
    this.winston.info(message, {
      ...context,
      component: 'api',
      type: 'request'
    });
  }

  /**
   * Log system health metrics
   */
  public health(message: string, context: {
    component: string;
    status: 'healthy' | 'degraded' | 'unhealthy';
    metrics?: {
      cpu?: number;
      memory?: number;
      connections?: number;
      latency?: number;
    };
  }): void {
    const level = context.status === 'unhealthy' ? 'error' :
                  context.status === 'degraded' ? 'warn' : 'info';
    
    this.winston.log(level, message, {
      ...context,
      type: 'health'
    });
  }

  /**
   * Create a timer for measuring operation duration
   */
  public timer(component: string, method: string): () => void {
    const start = Date.now();
    
    return () => {
      const duration = Date.now() - start;
      this.performance(`${component}.${method} completed`, {
        component,
        method,
        duration,
        success: true
      });
    };
  }

  /**
   * Log with custom level
   */
  public log(level: string, message: string, context?: LogContext): void {
    this.winston.log(level, message, context);
  }

  /**
   * Flush all log transports
   */
  public async flush(): Promise<void> {
    return new Promise((resolve) => {
      this.winston.on('finish', resolve);
      this.winston.end();
    });
  }

  /**
   * Get current log level
   */
  public getLevel(): string {
    return this.winston.level;
  }

  /**
   * Set log level
   */
  public setLevel(level: string): void {
    this.winston.level = level;
  }

  /**
   * Generate correlation ID
   */
  public static generateCorrelationId(): string {
    return uuidv4();
  }

  /**
   * Create logger instance with default configuration
   */
  public static create(service: string, environment?: string): Logger {
    return new Logger({
      level: process.env.LOG_LEVEL || 'info',
      service,
      environment: environment || process.env.NODE_ENV || 'development',
      enableFile: process.env.LOG_FILE_ENABLED === 'true'
    });
  }
}
