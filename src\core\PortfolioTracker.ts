/**
 * Portfolio Tracking System for AlphaMarketMaker
 * 
 * Real-time P&L calculation, position monitoring, and portfolio analytics
 * with comprehensive performance metrics and risk attribution.
 * 
 * Git Commit: "feat: add PortfolioTracker for real-time P&L and position monitoring"
 */

import { EventEmitter } from 'events';
import Big from 'big.js';
import { Config } from '@/utils/Config';
import { Logger } from '@/utils/Logger';
import { DatabaseManager, PositionRecord, TradeRecord, BalanceRecord } from '@/data/DatabaseManager';

export interface Position {
  symbol: string;
  exchange: string;
  size: string; // Positive for long, negative for short
  entryPrice: string;
  markPrice: string;
  unrealizedPnl: string;
  realizedPnl: string;
  totalPnl: string;
  percentage: string; // P&L as percentage of entry value
  lastUpdate: number;
}

export interface PortfolioSummary {
  totalValue: string;
  totalPnl: string;
  totalPnlPercent: string;
  realizedPnl: string;
  unrealizedPnl: string;
  dayPnl: string;
  dayPnlPercent: string;
  positions: Position[];
  balances: Balance[];
  lastUpdate: number;
}

export interface Balance {
  exchange: string;
  currency: string;
  total: string;
  available: string;
  locked: string;
  usdValue: string;
  lastUpdate: number;
}

export interface PerformanceMetrics {
  period: 'day' | 'week' | 'month' | 'year' | 'all';
  startDate: Date;
  endDate: Date;
  totalReturn: string;
  totalReturnPercent: string;
  sharpeRatio: number;
  maxDrawdown: string;
  maxDrawdownPercent: string;
  winRate: number;
  profitFactor: number;
  avgWin: string;
  avgLoss: string;
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  largestWin: string;
  largestLoss: string;
  avgHoldingPeriod: number; // in minutes
  volatility: number;
}

export interface TradeAnalysis {
  symbol: string;
  totalTrades: number;
  totalVolume: string;
  totalPnl: string;
  winRate: number;
  avgTradeSize: string;
  avgHoldingTime: number;
  bestTrade: string;
  worstTrade: string;
  fees: string;
  lastTradeTime: number;
}

export class PortfolioTracker extends EventEmitter {
  private config: Config;
  private logger: Logger;
  private dbManager: DatabaseManager;

  private positions: Map<string, Position> = new Map();
  private balances: Map<string, Balance> = new Map();
  private portfolioSummary: PortfolioSummary | null = null;
  private performanceMetrics: Map<string, PerformanceMetrics> = new Map();
  private tradeAnalytics: Map<string, TradeAnalysis> = new Map();

  private updateInterval: NodeJS.Timeout | null = null;
  private readonly UPDATE_INTERVAL = 5000; // 5 seconds
  private readonly PRICE_CACHE_TTL = 30000; // 30 seconds
  private priceCache: Map<string, { price: string; timestamp: number }> = new Map();

  constructor(config: Config, logger: Logger, dbManager: DatabaseManager) {
    super();
    this.config = config;
    this.logger = logger.child({ component: 'portfolio-tracker' });
    this.dbManager = dbManager;

    this.startPeriodicUpdates();
  }

  /**
   * Initialize portfolio tracker
   */
  public async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing portfolio tracker...');

      // Load initial data
      await this.loadPositions();
      await this.loadBalances();
      await this.updatePortfolioSummary();
      await this.calculatePerformanceMetrics();

      this.logger.info('Portfolio tracker initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize portfolio tracker:', error);
      throw error;
    }
  }

  /**
   * Update position from trade execution
   */
  public async updatePositionFromTrade(trade: TradeRecord): Promise<void> {
    try {
      const positionKey = `${trade.exchange}_${trade.symbol}`;
      const existingPosition = this.positions.get(positionKey);

      let newPosition: Position;

      if (existingPosition) {
        // Update existing position
        newPosition = await this.calculateUpdatedPosition(existingPosition, trade);
      } else {
        // Create new position
        newPosition = await this.createNewPosition(trade);
      }

      this.positions.set(positionKey, newPosition);

      // Save to database
      await this.savePositionToDatabase(newPosition);

      // Update portfolio summary
      await this.updatePortfolioSummary();

      this.emit('positionUpdated', newPosition);
      
      this.logger.info('Position updated from trade', {
        symbol: trade.symbol,
        exchange: trade.exchange,
        side: trade.side,
        amount: trade.amount,
        price: trade.price,
        newSize: newPosition.size
      });

    } catch (error) {
      this.logger.error('Failed to update position from trade:', error);
      throw error;
    }
  }

  /**
   * Calculate updated position from trade
   */
  private async calculateUpdatedPosition(position: Position, trade: TradeRecord): Promise<Position> {
    const currentSize = new Big(position.size);
    const currentEntryPrice = new Big(position.entryPrice);
    const tradeAmount = new Big(trade.amount);
    const tradePrice = new Big(trade.price);
    const tradeFee = new Big(trade.fee);

    // Calculate trade impact on position
    const tradeSize = trade.side === 'buy' ? tradeAmount : tradeAmount.mul(-1);
    const newSize = currentSize.plus(tradeSize);

    let newEntryPrice: Big;
    let realizedPnl = new Big(position.realizedPnl);

    if (newSize.eq(0)) {
      // Position closed
      newEntryPrice = new Big('0');
      
      // Calculate realized P&L for closing trade
      const closingPnl = trade.side === 'sell' 
        ? tradeAmount.mul(tradePrice.minus(currentEntryPrice)).minus(tradeFee)
        : tradeAmount.mul(currentEntryPrice.minus(tradePrice)).minus(tradeFee);
      
      realizedPnl = realizedPnl.plus(closingPnl);
    } else if (currentSize.mul(tradeSize).gte(0)) {
      // Adding to position - weighted average entry price
      const currentValue = currentSize.abs().mul(currentEntryPrice);
      const tradeValue = tradeAmount.mul(tradePrice);
      const totalValue = currentValue.plus(tradeValue);
      const totalSize = currentSize.abs().plus(tradeAmount);
      
      newEntryPrice = totalValue.div(totalSize);
    } else {
      // Reducing position
      if (newSize.abs().lt(currentSize.abs())) {
        // Partial close - realize some P&L
        const closedAmount = tradeAmount;
        const closingPnl = trade.side === 'sell'
          ? closedAmount.mul(tradePrice.minus(currentEntryPrice)).minus(tradeFee)
          : closedAmount.mul(currentEntryPrice.minus(tradePrice)).minus(tradeFee);
        
        realizedPnl = realizedPnl.plus(closingPnl);
        newEntryPrice = currentEntryPrice; // Keep same entry price
      } else {
        // Position reversal
        const closingAmount = currentSize.abs();
        const closingPnl = trade.side === 'sell'
          ? closingAmount.mul(tradePrice.minus(currentEntryPrice))
          : closingAmount.mul(currentEntryPrice.minus(tradePrice));
        
        realizedPnl = realizedPnl.plus(closingPnl);
        
        // New position with remaining amount
        const remainingAmount = tradeAmount.minus(closingAmount);
        newEntryPrice = tradePrice;
      }
    }

    // Get current mark price for unrealized P&L
    const markPrice = await this.getMarkPrice(trade.symbol);
    const unrealizedPnl = this.calculateUnrealizedPnl(newSize, newEntryPrice, new Big(markPrice));
    const totalPnl = realizedPnl.plus(unrealizedPnl);

    // Calculate percentage
    const entryValue = newSize.abs().mul(newEntryPrice);
    const percentage = entryValue.gt(0) ? totalPnl.div(entryValue).mul(100) : new Big('0');

    return {
      symbol: position.symbol,
      exchange: position.exchange,
      size: newSize.toFixed(8),
      entryPrice: newEntryPrice.toFixed(8),
      markPrice: markPrice,
      unrealizedPnl: unrealizedPnl.toFixed(8),
      realizedPnl: realizedPnl.toFixed(8),
      totalPnl: totalPnl.toFixed(8),
      percentage: percentage.toFixed(2),
      lastUpdate: Date.now()
    };
  }

  /**
   * Create new position from trade
   */
  private async createNewPosition(trade: TradeRecord): Promise<Position> {
    const tradeAmount = new Big(trade.amount);
    const tradePrice = new Big(trade.price);
    const tradeFee = new Big(trade.fee);

    const size = trade.side === 'buy' ? tradeAmount : tradeAmount.mul(-1);
    const entryPrice = tradePrice;
    const markPrice = await this.getMarkPrice(trade.symbol);
    
    const unrealizedPnl = this.calculateUnrealizedPnl(size, entryPrice, new Big(markPrice));
    const realizedPnl = tradeFee.mul(-1); // Fee is a realized loss
    const totalPnl = unrealizedPnl.plus(realizedPnl);

    const entryValue = size.abs().mul(entryPrice);
    const percentage = entryValue.gt(0) ? totalPnl.div(entryValue).mul(100) : new Big('0');

    return {
      symbol: trade.symbol,
      exchange: trade.exchange,
      size: size.toFixed(8),
      entryPrice: entryPrice.toFixed(8),
      markPrice: markPrice,
      unrealizedPnl: unrealizedPnl.toFixed(8),
      realizedPnl: realizedPnl.toFixed(8),
      totalPnl: totalPnl.toFixed(8),
      percentage: percentage.toFixed(2),
      lastUpdate: Date.now()
    };
  }

  /**
   * Calculate unrealized P&L
   */
  private calculateUnrealizedPnl(size: Big, entryPrice: Big, markPrice: Big): Big {
    if (size.eq(0)) {
      return new Big('0');
    }

    return size.mul(markPrice.minus(entryPrice));
  }

  /**
   * Get mark price for a symbol (with caching)
   */
  private async getMarkPrice(symbol: string): Promise<string> {
    const cached = this.priceCache.get(symbol);
    const now = Date.now();

    if (cached && (now - cached.timestamp) < this.PRICE_CACHE_TTL) {
      return cached.price;
    }

    try {
      // In a real implementation, this would fetch from market data manager
      // For now, return a placeholder
      const price = '50000'; // Placeholder price
      
      this.priceCache.set(symbol, { price, timestamp: now });
      return price;
    } catch (error) {
      this.logger.error(`Failed to get mark price for ${symbol}:`, error);
      return cached?.price || '0';
    }
  }

  /**
   * Load positions from database
   */
  private async loadPositions(): Promise<void> {
    try {
      const positionRecords = await this.dbManager.getPositions();
      
      for (const record of positionRecords) {
        const markPrice = await this.getMarkPrice(record.symbol);
        const size = new Big(record.size);
        const entryPrice = new Big(record.entryPrice);
        const unrealizedPnl = this.calculateUnrealizedPnl(size, entryPrice, new Big(markPrice));
        
        const position: Position = {
          symbol: record.symbol,
          exchange: record.exchange,
          size: record.size,
          entryPrice: record.entryPrice,
          markPrice: markPrice,
          unrealizedPnl: unrealizedPnl.toFixed(8),
          realizedPnl: record.realizedPnl,
          totalPnl: new Big(record.realizedPnl).plus(unrealizedPnl).toFixed(8),
          percentage: '0', // Will be calculated
          lastUpdate: record.timestamp.getTime()
        };

        // Calculate percentage
        const entryValue = size.abs().mul(entryPrice);
        if (entryValue.gt(0)) {
          const totalPnl = new Big(position.totalPnl);
          position.percentage = totalPnl.div(entryValue).mul(100).toFixed(2);
        }

        const positionKey = `${record.exchange}_${record.symbol}`;
        this.positions.set(positionKey, position);
      }

      this.logger.info(`Loaded ${positionRecords.length} positions`);
    } catch (error) {
      this.logger.error('Failed to load positions:', error);
    }
  }

  /**
   * Load balances from database
   */
  private async loadBalances(): Promise<void> {
    try {
      const balanceRecords = await this.dbManager.getBalances();
      
      for (const record of balanceRecords) {
        const balance: Balance = {
          exchange: record.exchange,
          currency: record.currency,
          total: record.total,
          available: record.available,
          locked: record.locked,
          usdValue: '0', // Would calculate based on current prices
          lastUpdate: record.timestamp.getTime()
        };

        const balanceKey = `${record.exchange}_${record.currency}`;
        this.balances.set(balanceKey, balance);
      }

      this.logger.info(`Loaded ${balanceRecords.length} balances`);
    } catch (error) {
      this.logger.error('Failed to load balances:', error);
    }
  }

  /**
   * Update portfolio summary
   */
  private async updatePortfolioSummary(): Promise<void> {
    try {
      let totalValue = new Big('0');
      let totalPnl = new Big('0');
      let realizedPnl = new Big('0');
      let unrealizedPnl = new Big('0');

      // Calculate totals from positions
      for (const position of this.positions.values()) {
        const positionValue = new Big(position.size).abs().mul(position.markPrice);
        totalValue = totalValue.plus(positionValue);
        totalPnl = totalPnl.plus(position.totalPnl);
        realizedPnl = realizedPnl.plus(position.realizedPnl);
        unrealizedPnl = unrealizedPnl.plus(position.unrealizedPnl);
      }

      // Add cash balances (simplified - would need USD conversion)
      for (const balance of this.balances.values()) {
        if (balance.currency === 'USD' || balance.currency === 'USDT') {
          totalValue = totalValue.plus(balance.total);
        }
      }

      // Calculate day P&L (would need historical data)
      const dayPnl = new Big('0'); // Placeholder

      const totalPnlPercent = totalValue.gt(0) ? totalPnl.div(totalValue).mul(100) : new Big('0');
      const dayPnlPercent = totalValue.gt(0) ? dayPnl.div(totalValue).mul(100) : new Big('0');

      this.portfolioSummary = {
        totalValue: totalValue.toFixed(2),
        totalPnl: totalPnl.toFixed(2),
        totalPnlPercent: totalPnlPercent.toFixed(2),
        realizedPnl: realizedPnl.toFixed(2),
        unrealizedPnl: unrealizedPnl.toFixed(2),
        dayPnl: dayPnl.toFixed(2),
        dayPnlPercent: dayPnlPercent.toFixed(2),
        positions: Array.from(this.positions.values()),
        balances: Array.from(this.balances.values()),
        lastUpdate: Date.now()
      };

      this.emit('portfolioUpdated', this.portfolioSummary);

    } catch (error) {
      this.logger.error('Failed to update portfolio summary:', error);
    }
  }

  /**
   * Calculate performance metrics
   */
  private async calculatePerformanceMetrics(): Promise<void> {
    try {
      const periods: Array<'day' | 'week' | 'month' | 'year' | 'all'> = ['day', 'week', 'month', 'year', 'all'];
      
      for (const period of periods) {
        const metrics = await this.calculatePeriodMetrics(period);
        this.performanceMetrics.set(period, metrics);
      }

      this.emit('metricsUpdated', Array.from(this.performanceMetrics.values()));

    } catch (error) {
      this.logger.error('Failed to calculate performance metrics:', error);
    }
  }

  /**
   * Calculate metrics for a specific period
   */
  private async calculatePeriodMetrics(period: 'day' | 'week' | 'month' | 'year' | 'all'): Promise<PerformanceMetrics> {
    const endDate = new Date();
    let startDate: Date;

    switch (period) {
      case 'day':
        startDate = new Date(endDate.getTime() - 24 * 60 * 60 * 1000);
        break;
      case 'week':
        startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case 'year':
        startDate = new Date(endDate.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      case 'all':
        startDate = new Date(0);
        break;
    }

    // Get trades for the period
    const { trades } = await this.dbManager.getTrades({
      startDate,
      endDate
    });

    // Calculate basic metrics
    let totalReturn = new Big('0');
    let winningTrades = 0;
    let losingTrades = 0;
    let totalWins = new Big('0');
    let totalLosses = new Big('0');
    let largestWin = new Big('0');
    let largestLoss = new Big('0');

    for (const trade of trades) {
      // Simplified P&L calculation
      const tradeValue = new Big(trade.amount).mul(trade.price);
      const tradePnl = trade.side === 'sell' ? tradeValue : tradeValue.mul(-1);
      
      totalReturn = totalReturn.plus(tradePnl);

      if (tradePnl.gt(0)) {
        winningTrades++;
        totalWins = totalWins.plus(tradePnl);
        if (tradePnl.gt(largestWin)) {
          largestWin = tradePnl;
        }
      } else if (tradePnl.lt(0)) {
        losingTrades++;
        totalLosses = totalLosses.plus(tradePnl.abs());
        if (tradePnl.abs().gt(largestLoss)) {
          largestLoss = tradePnl.abs();
        }
      }
    }

    const totalTrades = trades.length;
    const winRate = totalTrades > 0 ? winningTrades / totalTrades : 0;
    const avgWin = winningTrades > 0 ? totalWins.div(winningTrades) : new Big('0');
    const avgLoss = losingTrades > 0 ? totalLosses.div(losingTrades) : new Big('0');
    const profitFactor = totalLosses.gt(0) ? totalWins.div(totalLosses).toNumber() : 0;

    return {
      period,
      startDate,
      endDate,
      totalReturn: totalReturn.toFixed(2),
      totalReturnPercent: '0', // Would need initial capital
      sharpeRatio: 0, // Would need returns data
      maxDrawdown: '0', // Would need equity curve
      maxDrawdownPercent: '0',
      winRate,
      profitFactor,
      avgWin: avgWin.toFixed(2),
      avgLoss: avgLoss.toFixed(2),
      totalTrades,
      winningTrades,
      losingTrades,
      largestWin: largestWin.toFixed(2),
      largestLoss: largestLoss.toFixed(2),
      avgHoldingPeriod: 0, // Would calculate from trade timestamps
      volatility: 0 // Would calculate from returns
    };
  }

  /**
   * Save position to database
   */
  private async savePositionToDatabase(position: Position): Promise<void> {
    try {
      const positionRecord: PositionRecord = {
        exchange: position.exchange,
        symbol: position.symbol,
        size: position.size,
        entryPrice: position.entryPrice,
        markPrice: position.markPrice,
        unrealizedPnl: position.unrealizedPnl,
        realizedPnl: position.realizedPnl,
        timestamp: new Date(position.lastUpdate)
      };

      await this.dbManager.upsertPosition(positionRecord);
    } catch (error) {
      this.logger.error('Failed to save position to database:', error);
    }
  }

  /**
   * Start periodic updates
   */
  private startPeriodicUpdates(): void {
    this.updateInterval = setInterval(async () => {
      try {
        await this.updatePortfolioSummary();
      } catch (error) {
        this.logger.error('Periodic update failed:', error);
      }
    }, this.UPDATE_INTERVAL);

    this.logger.info('Started periodic portfolio updates');
  }

  /**
   * Get position for a symbol
   */
  public getPosition(symbol: string, exchange?: string): Position | null {
    if (exchange) {
      return this.positions.get(`${exchange}_${symbol}`) || null;
    }

    // Return first position found for symbol across all exchanges
    for (const position of this.positions.values()) {
      if (position.symbol === symbol) {
        return position;
      }
    }

    return null;
  }

  /**
   * Get all positions
   */
  public getAllPositions(): Position[] {
    return Array.from(this.positions.values());
  }

  /**
   * Get portfolio summary
   */
  public getPortfolioSummary(): PortfolioSummary | null {
    return this.portfolioSummary;
  }

  /**
   * Get performance metrics
   */
  public getPerformanceMetrics(period?: string): PerformanceMetrics[] {
    if (period) {
      const metrics = this.performanceMetrics.get(period);
      return metrics ? [metrics] : [];
    }
    
    return Array.from(this.performanceMetrics.values());
  }

  /**
   * Stop portfolio tracker
   */
  public stop(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    
    this.positions.clear();
    this.balances.clear();
    this.performanceMetrics.clear();
    this.priceCache.clear();
    
    this.logger.info('Portfolio tracker stopped');
  }
}
