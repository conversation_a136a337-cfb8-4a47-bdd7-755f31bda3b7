<!DOCTYPE html>
<html lang="en" class="dark">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  
  <!-- SEO Meta Tags -->
  <title>AlphaMarketMaker - Professional Cryptocurrency Market Making Dashboard</title>
  <meta name="description" content="Advanced cryptocurrency market making platform with real-time portfolio tracking, risk management, and multi-exchange trading capabilities." />
  <meta name="keywords" content="cryptocurrency, market making, trading bot, portfolio management, risk management, binance, coinbase, kraken" />
  <meta name="author" content="AlphaMarketMaker Team" />
  <meta name="robots" content="index, follow" />
  
  <!-- Open Graph Meta Tags -->
  <meta property="og:title" content="AlphaMarketMaker - Professional Cryptocurrency Market Making" />
  <meta property="og:description" content="Advanced cryptocurrency market making platform with real-time portfolio tracking and risk management." />
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://alphamarketmaker.com" />
  <meta property="og:image" content="https://alphamarketmaker.com/og-image.png" />
  <meta property="og:site_name" content="AlphaMarketMaker" />
  
  <!-- Twitter Card Meta Tags -->
  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:title" content="AlphaMarketMaker - Professional Cryptocurrency Market Making" />
  <meta name="twitter:description" content="Advanced cryptocurrency market making platform with real-time portfolio tracking and risk management." />
  <meta name="twitter:image" content="https://alphamarketmaker.com/twitter-image.png" />
  <meta name="twitter:creator" content="@alphamarketmaker" />
  
  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
  <link rel="icon" type="image/png" href="/favicon.png" />
  <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
  
  <!-- Manifest for PWA -->
  <link rel="manifest" href="/manifest.json" />
  
  <!-- Theme Color -->
  <meta name="theme-color" content="#1e293b" />
  <meta name="msapplication-TileColor" content="#1e293b" />
  
  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet" />
  
  <!-- Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "AlphaMarketMaker",
    "description": "Professional cryptocurrency market making platform with real-time portfolio tracking, risk management, and multi-exchange trading capabilities.",
    "url": "https://alphamarketmaker.com",
    "applicationCategory": "FinanceApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "author": {
      "@type": "Organization",
      "name": "AlphaMarketMaker Team"
    },
    "screenshot": "https://alphamarketmaker.com/screenshot.png",
    "featureList": [
      "Real-time market making",
      "Multi-exchange support",
      "Portfolio tracking",
      "Risk management",
      "Performance analytics",
      "WebSocket real-time updates"
    ]
  }
  </script>
  
  <!-- Security Headers -->
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' ws: wss:;" />
  <meta http-equiv="X-Content-Type-Options" content="nosniff" />
  <meta http-equiv="X-Frame-Options" content="DENY" />
  <meta http-equiv="X-XSS-Protection" content="1; mode=block" />
  
  <!-- Preload critical resources -->
  <link rel="preload" href="/src/main.tsx" as="script" />
  <link rel="preload" href="/src/index.css" as="style" />
  
  <!-- Critical CSS (inline for performance) -->
  <style>
    /* Critical CSS for initial render */
    body {
      margin: 0;
      font-family: 'Inter', system-ui, -apple-system, sans-serif;
      background-color: #0f172a;
      color: #f1f5f9;
      overflow-x: hidden;
    }
    
    /* Loading spinner */
    .loading-spinner {
      display: inline-block;
      width: 40px;
      height: 40px;
      border: 3px solid #334155;
      border-radius: 50%;
      border-top-color: #3b82f6;
      animation: spin 1s ease-in-out infinite;
    }
    
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
    
    /* Initial loading screen */
    .loading-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 9999;
    }
    
    .loading-logo {
      font-size: 2rem;
      font-weight: 700;
      color: #3b82f6;
      margin-bottom: 2rem;
      text-align: center;
    }
    
    .loading-text {
      color: #64748b;
      margin-top: 1rem;
      font-size: 0.875rem;
    }
    
    /* Hide loading screen when app loads */
    .app-loaded .loading-screen {
      display: none;
    }
  </style>
</head>
<body>
  <!-- Loading Screen -->
  <div class="loading-screen" id="loading-screen">
    <div class="loading-logo">
      <div style="display: flex; align-items: center; gap: 0.5rem;">
        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M16 2L20.5 11.5L30 16L20.5 20.5L16 30L11.5 20.5L2 16L11.5 11.5L16 2Z" fill="#3b82f6"/>
        </svg>
        AlphaMarketMaker
      </div>
    </div>
    <div class="loading-spinner"></div>
    <div class="loading-text">Loading trading dashboard...</div>
  </div>
  
  <!-- React App Root -->
  <div id="root"></div>
  
  <!-- Service Worker Registration -->
  <script>
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
          .then((registration) => {
            console.log('SW registered: ', registration);
          })
          .catch((registrationError) => {
            console.log('SW registration failed: ', registrationError);
          });
      });
    }
  </script>
  
  <!-- Main Application Script -->
  <script type="module" src="/src/main.tsx"></script>
  
  <!-- Analytics (placeholder) -->
  <script>
    // Add your analytics code here
    // Example: Google Analytics, Mixpanel, etc.
  </script>
</body>
</html>
