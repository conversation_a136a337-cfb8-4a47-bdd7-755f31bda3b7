/**
 * Configuration Management for AlphaMarketMaker
 * 
 * Centralized configuration management with environment-specific settings,
 * validation, and type safety for all application components.
 */

import * as dotenv from 'dotenv';
import * as fs from 'fs';
import * as path from 'path';

// Load environment variables
dotenv.config();

export interface ExchangeConfig {
  enabled: boolean;
  weight: number;
  apiKey: string;
  secretKey: string;
  sandbox?: boolean;
  rateLimit?: number;
  timeout?: number;
}

export interface TradingPairConfig {
  symbol: string;
  minSpread: number;
  maxSpread: number;
  orderSize: number;
  maxPosition: number;
  enabled: boolean;
  tickSize?: number;
  minOrderSize?: number;
}

export interface RiskLimits {
  maxDailyLoss: number;
  maxDrawdown: number;
  inventoryLimit: number;
  maxPositionSize: number;
  stopLossPercent: number;
  takeProfitPercent?: number;
  maxOpenOrders: number;
}

export interface DatabaseConfig {
  url: string;
  type: 'postgresql' | 'sqlite';
  pool?: {
    min: number;
    max: number;
  };
  migrations?: {
    directory: string;
  };
}

export interface CacheConfig {
  url: string;
  ttl: number;
  maxMemory?: string;
}

export interface LoggingConfig {
  level: string;
  format: string;
  file?: {
    enabled: boolean;
    filename: string;
    maxSize: string;
    maxFiles: number;
  };
}

export interface APIConfig {
  port: number;
  host: string;
  cors: {
    origin: string | string[];
    credentials: boolean;
  };
  rateLimit: {
    windowMs: number;
    max: number;
  };
  jwt: {
    secret: string;
    expiresIn: string;
  };
}

export interface MarketMakingConfig {
  enabled: boolean;
  pairs: TradingPairConfig[];
  exchanges: Record<string, ExchangeConfig>;
  riskLimits: RiskLimits;
  spreadCalculation: {
    method: 'fixed' | 'dynamic' | 'volatility';
    volatilityWindow: number;
    inventorySkew: boolean;
    depthWeight: number;
  };
  orderManagement: {
    refreshInterval: number;
    cancelTimeout: number;
    maxRetries: number;
  };
}

export class Config {
  private config: any;
  private environment: string;

  constructor() {
    this.environment = process.env.NODE_ENV || 'development';
    this.loadConfiguration();
    this.validateConfiguration();
  }

  /**
   * Load configuration from environment and config files
   */
  private loadConfiguration(): void {
    // Base configuration
    this.config = {
      environment: this.environment,
      
      // API Configuration
      api: {
        port: parseInt(process.env.PORT || '8080', 10),
        host: process.env.HOST || '0.0.0.0',
        cors: {
          origin: process.env.FRONTEND_URL || 'http://localhost:3000',
          credentials: true
        },
        rateLimit: {
          windowMs: parseInt(process.env.RATE_LIMIT_WINDOW || '900000', 10), // 15 minutes
          max: parseInt(process.env.RATE_LIMIT_MAX || '1000', 10)
        },
        jwt: {
          secret: process.env.JWT_SECRET || 'your-secret-key',
          expiresIn: process.env.JWT_EXPIRES_IN || '24h'
        }
      },

      // Database Configuration
      database: {
        url: process.env.DATABASE_URL || 'sqlite:./data/alphamarketmaker.db',
        type: process.env.DATABASE_URL?.startsWith('postgresql') ? 'postgresql' : 'sqlite',
        pool: {
          min: parseInt(process.env.DB_POOL_MIN || '2', 10),
          max: parseInt(process.env.DB_POOL_MAX || '10', 10)
        },
        migrations: {
          directory: './migrations'
        }
      },

      // Cache Configuration
      cache: {
        url: process.env.REDIS_URL || 'redis://localhost:6379',
        ttl: parseInt(process.env.CACHE_TTL || '300', 10), // 5 minutes
        maxMemory: process.env.REDIS_MAX_MEMORY || '256mb'
      },

      // Logging Configuration
      logging: {
        level: process.env.LOG_LEVEL || 'info',
        format: process.env.LOG_FORMAT || 'json',
        file: {
          enabled: process.env.LOG_FILE_ENABLED === 'true',
          filename: process.env.LOG_FILENAME || 'logs/alphamarketmaker-%DATE%.log',
          maxSize: process.env.LOG_MAX_SIZE || '20m',
          maxFiles: parseInt(process.env.LOG_MAX_FILES || '14', 10)
        }
      },

      // Exchange Configurations
      exchanges: {
        binance: {
          enabled: process.env.BINANCE_ENABLED !== 'false',
          weight: parseFloat(process.env.BINANCE_WEIGHT || '0.4'),
          apiKey: process.env.BINANCE_API_KEY || '',
          secretKey: process.env.BINANCE_SECRET_KEY || '',
          sandbox: process.env.BINANCE_SANDBOX === 'true',
          rateLimit: parseInt(process.env.BINANCE_RATE_LIMIT || '1200', 10),
          timeout: parseInt(process.env.BINANCE_TIMEOUT || '10000', 10)
        },
        coinbase: {
          enabled: process.env.COINBASE_ENABLED !== 'false',
          weight: parseFloat(process.env.COINBASE_WEIGHT || '0.3'),
          apiKey: process.env.COINBASE_API_KEY || '',
          secretKey: process.env.COINBASE_SECRET || '',
          sandbox: process.env.COINBASE_SANDBOX === 'true',
          rateLimit: parseInt(process.env.COINBASE_RATE_LIMIT || '10', 10),
          timeout: parseInt(process.env.COINBASE_TIMEOUT || '10000', 10)
        },
        kraken: {
          enabled: process.env.KRAKEN_ENABLED !== 'false',
          weight: parseFloat(process.env.KRAKEN_WEIGHT || '0.3'),
          apiKey: process.env.KRAKEN_API_KEY || '',
          secretKey: process.env.KRAKEN_SECRET || '',
          sandbox: process.env.KRAKEN_SANDBOX === 'true',
          rateLimit: parseInt(process.env.KRAKEN_RATE_LIMIT || '1', 10),
          timeout: parseInt(process.env.KRAKEN_TIMEOUT || '10000', 10)
        }
      },

      // Risk Management
      riskLimits: {
        maxDailyLoss: parseFloat(process.env.MAX_DAILY_LOSS || '1000'),
        maxDrawdown: parseFloat(process.env.MAX_DRAWDOWN || '0.1'),
        inventoryLimit: parseFloat(process.env.INVENTORY_LIMIT || '0.5'),
        maxPositionSize: parseFloat(process.env.MAX_POSITION_SIZE || '0.02'),
        stopLossPercent: parseFloat(process.env.STOP_LOSS_PCT || '0.05'),
        takeProfitPercent: parseFloat(process.env.TAKE_PROFIT_PCT || '0.02'),
        maxOpenOrders: parseInt(process.env.MAX_OPEN_ORDERS || '20', 10)
      },

      // Market Making Configuration
      marketMaking: {
        enabled: process.env.MARKET_MAKING_ENABLED !== 'false',
        spreadCalculation: {
          method: process.env.SPREAD_METHOD || 'dynamic',
          volatilityWindow: parseInt(process.env.VOLATILITY_WINDOW || '100', 10),
          inventorySkew: process.env.INVENTORY_SKEW !== 'false',
          depthWeight: parseFloat(process.env.DEPTH_WEIGHT || '0.3')
        },
        orderManagement: {
          refreshInterval: parseInt(process.env.ORDER_REFRESH_INTERVAL || '5000', 10),
          cancelTimeout: parseInt(process.env.ORDER_CANCEL_TIMEOUT || '30000', 10),
          maxRetries: parseInt(process.env.ORDER_MAX_RETRIES || '3', 10)
        }
      }
    };

    // Load environment-specific configuration file if it exists
    this.loadConfigFile();
  }

  /**
   * Load configuration from JSON file
   */
  private loadConfigFile(): void {
    const configPath = path.join(process.cwd(), 'config', `${this.environment}.json`);
    
    if (fs.existsSync(configPath)) {
      try {
        const fileConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        this.config = this.mergeDeep(this.config, fileConfig);
      } catch (error) {
        console.warn(`Failed to load config file ${configPath}:`, error);
      }
    }
  }

  /**
   * Deep merge two objects
   */
  private mergeDeep(target: any, source: any): any {
    const output = Object.assign({}, target);
    
    if (this.isObject(target) && this.isObject(source)) {
      Object.keys(source).forEach(key => {
        if (this.isObject(source[key])) {
          if (!(key in target)) {
            Object.assign(output, { [key]: source[key] });
          } else {
            output[key] = this.mergeDeep(target[key], source[key]);
          }
        } else {
          Object.assign(output, { [key]: source[key] });
        }
      });
    }
    
    return output;
  }

  /**
   * Check if value is an object
   */
  private isObject(item: any): boolean {
    return item && typeof item === 'object' && !Array.isArray(item);
  }

  /**
   * Validate configuration
   */
  private validateConfiguration(): void {
    const requiredFields = [
      'api.jwt.secret',
      'database.url'
    ];

    for (const field of requiredFields) {
      if (!this.get(field)) {
        throw new Error(`Missing required configuration field: ${field}`);
      }
    }

    // Validate exchange configurations
    const enabledExchanges = Object.entries(this.config.exchanges)
      .filter(([_, config]: [string, any]) => config.enabled);

    if (enabledExchanges.length === 0) {
      throw new Error('At least one exchange must be enabled');
    }

    for (const [name, config] of enabledExchanges) {
      if (!config.apiKey || !config.secretKey) {
        throw new Error(`Missing API credentials for exchange: ${name}`);
      }
    }
  }

  /**
   * Get configuration value by path
   */
  public get(path: string): any {
    return path.split('.').reduce((obj, key) => obj?.[key], this.config);
  }

  /**
   * Get all configuration
   */
  public getAll(): any {
    return { ...this.config };
  }

  /**
   * Get API configuration
   */
  public getAPI(): APIConfig {
    return this.config.api;
  }

  /**
   * Get database configuration
   */
  public getDatabase(): DatabaseConfig {
    return this.config.database;
  }

  /**
   * Get cache configuration
   */
  public getCache(): CacheConfig {
    return this.config.cache;
  }

  /**
   * Get logging configuration
   */
  public getLogging(): LoggingConfig {
    return this.config.logging;
  }

  /**
   * Get exchange configuration
   */
  public getExchange(name: string): ExchangeConfig {
    return this.config.exchanges[name];
  }

  /**
   * Get all enabled exchanges
   */
  public getEnabledExchanges(): Record<string, ExchangeConfig> {
    return Object.entries(this.config.exchanges)
      .filter(([_, config]: [string, any]) => config.enabled)
      .reduce((acc, [name, config]) => ({ ...acc, [name]: config }), {});
  }

  /**
   * Get risk limits
   */
  public getRiskLimits(): RiskLimits {
    return this.config.riskLimits;
  }

  /**
   * Get market making configuration
   */
  public getMarketMaking(): MarketMakingConfig {
    return this.config.marketMaking;
  }

  /**
   * Check if running in development mode
   */
  public isDevelopment(): boolean {
    return this.environment === 'development';
  }

  /**
   * Check if running in production mode
   */
  public isProduction(): boolean {
    return this.environment === 'production';
  }
}
