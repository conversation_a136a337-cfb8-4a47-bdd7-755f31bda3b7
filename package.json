{"name": "alphamarketmaker", "version": "1.0.0", "description": "High-performance cryptocurrency market making bot with multi-exchange support", "main": "dist/app.js", "scripts": {"start": "node dist/app.js", "dev": "nodemon --exec ts-node src/app.ts", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "test:performance": "jest --testPathPattern=performance", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "db:migrate": "knex migrate:latest", "db:seed": "knex seed:run", "db:rollback": "knex migrate:rollback", "docker:build": "docker build -t alphamarketmaker .", "docker:run": "docker run -p 3000:3000 -p 8080:8080 alphamarketmaker", "deploy:netlify": "./scripts/deploy-netlify.sh", "deploy:aws": "./scripts/deploy-aws.sh", "deploy:gcp": "./scripts/deploy-gcp.sh"}, "keywords": ["cryptocurrency", "market-making", "trading-bot", "binance", "coinbase", "kraken", "liquidity", "algorithmic-trading", "typescript", "websocket"], "author": "AlphaMarketMaker Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/AlphaMarketMaker.git"}, "bugs": {"url": "https://github.com/yourusername/AlphaMarketMaker/issues"}, "homepage": "https://github.com/yourusername/AlphaMarketMaker#readme", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "dependencies": {"express": "^4.18.2", "express-rate-limit": "^6.7.0", "express-validator": "^6.15.0", "helmet": "^6.1.5", "cors": "^2.8.5", "ws": "^8.13.0", "socket.io": "^4.6.2", "jsonwebtoken": "^9.0.0", "bcryptjs": "^2.4.3", "dotenv": "^16.0.3", "winston": "^3.8.2", "winston-daily-rotate-file": "^4.7.1", "pg": "^8.11.0", "redis": "^4.6.6", "knex": "^2.4.2", "axios": "^1.4.0", "crypto-js": "^4.1.1", "node-cron": "^3.0.2", "uuid": "^9.0.0", "lodash": "^4.17.21", "moment": "^2.29.4", "big.js": "^6.2.1", "ccxt": "^3.0.119", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"@types/node": "^18.16.3", "@types/express": "^4.17.17", "@types/ws": "^8.5.4", "@types/jsonwebtoken": "^9.0.2", "@types/bcryptjs": "^2.4.2", "@types/cors": "^2.8.13", "@types/uuid": "^9.0.1", "@types/lodash": "^4.14.194", "@types/big.js": "^6.1.6", "@types/compression": "^1.7.2", "@types/morgan": "^1.9.4", "@types/jest": "^29.5.1", "@types/supertest": "^2.0.12", "typescript": "^5.0.4", "ts-node": "^10.9.1", "nodemon": "^2.0.22", "jest": "^29.5.0", "ts-jest": "^29.1.0", "supertest": "^6.3.3", "eslint": "^8.40.0", "@typescript-eslint/eslint-plugin": "^5.59.6", "@typescript-eslint/parser": "^5.59.6", "prettier": "^2.8.8", "husky": "^8.0.3", "lint-staged": "^13.2.2"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src", "<rootDir>/tests"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/app.ts"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}, "lint-staged": {"*.ts": ["eslint --fix", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm test"}}}