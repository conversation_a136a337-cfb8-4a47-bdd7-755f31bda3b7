/**
 * Authentication Context for AlphaMarketMaker Dashboard
 * 
 * React context provider for managing authentication state,
 * JWT tokens, and user session management across the application.
 * 
 * Git Commit: "feat: add AuthContext for global authentication state management"
 */

import React, { createContext, useCallback, useEffect, useState, ReactNode } from 'react';
import toast from 'react-hot-toast';
import { apiService } from '../services/apiService';
import type { User, LoginCredentials, AuthHook } from '../hooks/useAuth';

// Storage keys
const STORAGE_KEYS = {
  USER: 'alphamarketmaker_user',
  TOKEN: 'alphamarketmaker_token',
  REFRESH_TOKEN: 'alphamarketmaker_refresh_token'
};

// Create context
export const AuthContext = createContext<AuthHook | null>(null);

// Auth provider props
interface AuthProviderProps {
  children: ReactNode;
}

// Auth provider component
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Check if token is expired
  const isTokenExpired = useCallback((): boolean => {
    if (!user?.expiresAt) return true;
    return Date.now() >= user.expiresAt;
  }, [user?.expiresAt]);

  // Check if user has specific permission
  const hasPermission = useCallback((permission: string): boolean => {
    if (!user?.permissions) return false;
    return user.permissions.includes(permission) || user.permissions.includes('admin');
  }, [user?.permissions]);

  // Save user data to localStorage
  const saveUserData = useCallback((userData: User) => {
    try {
      localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(userData));
      localStorage.setItem(STORAGE_KEYS.TOKEN, userData.token);
      if (userData.refreshToken) {
        localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, userData.refreshToken);
      }
    } catch (error) {
      console.error('Failed to save user data to localStorage:', error);
    }
  }, []);

  // Clear user data from localStorage
  const clearUserData = useCallback(() => {
    try {
      localStorage.removeItem(STORAGE_KEYS.USER);
      localStorage.removeItem(STORAGE_KEYS.TOKEN);
      localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
    } catch (error) {
      console.error('Failed to clear user data from localStorage:', error);
    }
  }, []);

  // Load user data from localStorage
  const loadUserData = useCallback((): User | null => {
    try {
      const userData = localStorage.getItem(STORAGE_KEYS.USER);
      if (!userData) return null;

      const parsedUser = JSON.parse(userData) as User;
      
      // Check if token is expired
      if (Date.now() >= parsedUser.expiresAt) {
        clearUserData();
        return null;
      }

      return parsedUser;
    } catch (error) {
      console.error('Failed to load user data from localStorage:', error);
      clearUserData();
      return null;
    }
  }, [clearUserData]);

  // Login function
  const login = useCallback(async (credentials: LoginCredentials): Promise<void> => {
    try {
      setIsLoading(true);
      
      const response = await apiService.post('/auth/login', credentials);
      
      if (!response.success) {
        throw new Error(response.error || 'Login failed');
      }

      const { token, user: userData, expiresIn } = response.data;
      
      // Calculate expiration time
      const expiresAt = Date.now() + (expiresIn ? parseInt(expiresIn) * 1000 : 24 * 60 * 60 * 1000);
      
      const userWithToken: User = {
        ...userData,
        token,
        expiresAt
      };

      setUser(userWithToken);
      saveUserData(userWithToken);
      
      // Set default authorization header
      apiService.setAuthToken(token);
      
      toast.success('Login successful');
    } catch (error) {
      console.error('Login error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      toast.error(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [saveUserData]);

  // Logout function
  const logout = useCallback(() => {
    try {
      setUser(null);
      clearUserData();
      apiService.clearAuthToken();
      toast.success('Logged out successfully');
    } catch (error) {
      console.error('Logout error:', error);
      toast.error('Logout failed');
    }
  }, [clearUserData]);

  // Refresh token function
  const refreshToken = useCallback(async (): Promise<void> => {
    try {
      const storedRefreshToken = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
      
      if (!storedRefreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await apiService.post('/auth/refresh', {
        refreshToken: storedRefreshToken
      });

      if (!response.success) {
        throw new Error(response.error || 'Token refresh failed');
      }

      const { token, user: userData, expiresIn } = response.data;
      
      // Calculate expiration time
      const expiresAt = Date.now() + (expiresIn ? parseInt(expiresIn) * 1000 : 24 * 60 * 60 * 1000);
      
      const userWithToken: User = {
        ...userData,
        token,
        expiresAt
      };

      setUser(userWithToken);
      saveUserData(userWithToken);
      
      // Update authorization header
      apiService.setAuthToken(token);
      
      console.log('Token refreshed successfully');
    } catch (error) {
      console.error('Token refresh error:', error);
      // If refresh fails, logout user
      logout();
      throw error;
    }
  }, [logout, saveUserData]);

  // Initialize authentication state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        setIsLoading(true);
        
        const userData = loadUserData();
        
        if (userData) {
          // Check if token is close to expiring (within 5 minutes)
          const timeUntilExpiry = userData.expiresAt - Date.now();
          const fiveMinutes = 5 * 60 * 1000;
          
          if (timeUntilExpiry <= 0) {
            // Token expired, try to refresh
            try {
              await refreshToken();
            } catch (error) {
              console.log('Token refresh failed during initialization');
              clearUserData();
            }
          } else if (timeUntilExpiry <= fiveMinutes) {
            // Token expiring soon, refresh proactively
            try {
              await refreshToken();
            } catch (error) {
              console.log('Proactive token refresh failed');
              // Continue with existing token if refresh fails
              setUser(userData);
              apiService.setAuthToken(userData.token);
            }
          } else {
            // Token is valid
            setUser(userData);
            apiService.setAuthToken(userData.token);
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        clearUserData();
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, [loadUserData, refreshToken, clearUserData]);

  // Set up automatic token refresh
  useEffect(() => {
    if (!user?.expiresAt) return;

    const timeUntilExpiry = user.expiresAt - Date.now();
    const refreshTime = Math.max(timeUntilExpiry - 5 * 60 * 1000, 60 * 1000); // Refresh 5 minutes before expiry, but at least 1 minute from now

    const refreshTimeout = setTimeout(async () => {
      try {
        await refreshToken();
      } catch (error) {
        console.error('Automatic token refresh failed:', error);
      }
    }, refreshTime);

    return () => clearTimeout(refreshTimeout);
  }, [user?.expiresAt, refreshToken]);

  // Handle API authentication errors
  useEffect(() => {
    const handleAuthError = () => {
      console.log('Authentication error detected, logging out');
      logout();
    };

    // Listen for authentication errors from API service
    window.addEventListener('auth-error', handleAuthError);

    return () => {
      window.removeEventListener('auth-error', handleAuthError);
    };
  }, [logout]);

  // Context value
  const contextValue: AuthHook = {
    user,
    isAuthenticated: !!user && !isTokenExpired(),
    isLoading,
    login,
    logout,
    refreshToken,
    hasPermission,
    isTokenExpired
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};
