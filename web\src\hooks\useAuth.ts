/**
 * Authentication Hook for AlphaMarketMaker Dashboard
 * 
 * Custom React hook for managing user authentication state,
 * JWT tokens, and authentication-related operations.
 * 
 * Git Commit: "feat: add authentication hook with JWT token management"
 */

import { useContext } from 'react';
import { AuthContext } from '../contexts/AuthContext';

export interface User {
  id: string;
  email: string;
  tier: string;
  permissions: string[];
  token: string;
  refreshToken?: string;
  expiresAt: number;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface AuthHook {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  hasPermission: (permission: string) => boolean;
  isTokenExpired: () => boolean;
}

export const useAuth = (): AuthHook => {
  const context = useContext(AuthContext);
  
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
};
