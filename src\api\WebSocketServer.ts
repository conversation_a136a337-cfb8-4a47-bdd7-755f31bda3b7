/**
 * WebSocket Server for AlphaMarketMaker Real-time Updates
 * 
 * High-performance WebSocket server with authentication, rate limiting,
 * and real-time market data, portfolio, and trading updates for the dashboard.
 * 
 * Git Commit: "feat: add WebSocket server for real-time dashboard updates"
 */

import { Server as SocketIOServer, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import { Config } from '@/utils/Config';
import { Logger } from '@/utils/Logger';
import { MarketDataManager } from '@/data/MarketDataManager';
import { PortfolioTracker } from '@/core/PortfolioTracker';

export interface AuthenticatedSocket extends Socket {
  user?: {
    id: string;
    email: string;
    tier: string;
    permissions: string[];
  };
  subscriptions?: Set<string>;
  rateLimitTokens?: number;
  lastTokenRefill?: number;
}

export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: number;
  requestId?: string;
}

export interface SubscriptionRequest {
  channel: string;
  symbol?: string;
  interval?: string;
}

export interface RateLimitConfig {
  tokensPerSecond: number;
  maxTokens: number;
  messagesPerMinute: number;
}

export class WebSocketServer {
  private io: SocketIOServer;
  private config: Config;
  private logger: Logger;
  private marketDataManager: MarketDataManager;
  private portfolioTracker: PortfolioTracker;

  private connectedClients: Map<string, AuthenticatedSocket> = new Map();
  private subscriptions: Map<string, Set<string>> = new Map(); // channel -> socketIds
  private rateLimitConfig: RateLimitConfig;
  private updateIntervals: Map<string, NodeJS.Timeout> = new Map();

  // Rate limiting configuration
  private readonly DEFAULT_RATE_LIMIT: RateLimitConfig = {
    tokensPerSecond: 10,
    maxTokens: 50,
    messagesPerMinute: 100
  };

  // Update intervals (in milliseconds)
  private readonly UPDATE_INTERVALS = {
    portfolio: 1000,    // 1 second
    positions: 2000,    // 2 seconds
    risk: 5000,         // 5 seconds
    market: 500,        // 500ms
    orders: 1000        // 1 second
  };

  constructor(
    io: SocketIOServer,
    config: Config,
    logger: Logger,
    marketDataManager: MarketDataManager,
    portfolioTracker: PortfolioTracker
  ) {
    this.io = io;
    this.config = config;
    this.logger = logger.child({ component: 'websocket-server' });
    this.marketDataManager = marketDataManager;
    this.portfolioTracker = portfolioTracker;
    this.rateLimitConfig = this.DEFAULT_RATE_LIMIT;

    this.setupMiddleware();
    this.setupEventHandlers();
    this.startPeriodicUpdates();
  }

  /**
   * Setup WebSocket middleware
   */
  private setupMiddleware(): void {
    // Authentication middleware
    this.io.use(async (socket: AuthenticatedSocket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
          return next(new Error('Authentication required'));
        }

        const decoded = jwt.verify(token, this.config.get('api.jwt.secret')) as any;
        
        socket.user = {
          id: decoded.userId,
          email: decoded.email,
          tier: decoded.tier || 'free',
          permissions: decoded.permissions || []
        };

        // Initialize rate limiting
        socket.rateLimitTokens = this.rateLimitConfig.maxTokens;
        socket.lastTokenRefill = Date.now();
        socket.subscriptions = new Set();

        this.logger.info('WebSocket client authenticated', {
          userId: socket.user.id,
          socketId: socket.id
        });

        next();
      } catch (error) {
        this.logger.error('WebSocket authentication failed:', error);
        next(new Error('Authentication failed'));
      }
    });

    // Rate limiting middleware
    this.io.use((socket: AuthenticatedSocket, next) => {
      const originalEmit = socket.emit;
      
      socket.emit = function(event: string, ...args: any[]) {
        if (!socket.user) {
          return originalEmit.call(this, event, ...args);
        }

        // Check rate limit
        const now = Date.now();
        const timeSinceRefill = now - (socket.lastTokenRefill || now);
        const tokensToAdd = Math.floor(timeSinceRefill / 1000) * this.rateLimitConfig.tokensPerSecond;
        
        socket.rateLimitTokens = Math.min(
          this.rateLimitConfig.maxTokens,
          (socket.rateLimitTokens || 0) + tokensToAdd
        );
        socket.lastTokenRefill = now;

        if ((socket.rateLimitTokens || 0) <= 0) {
          this.logger.warn('WebSocket rate limit exceeded', {
            userId: socket.user?.id,
            socketId: socket.id
          });
          return false;
        }

        socket.rateLimitTokens = (socket.rateLimitTokens || 0) - 1;
        return originalEmit.call(this, event, ...args);
      }.bind(this);

      next();
    });
  }

  /**
   * Setup WebSocket event handlers
   */
  private setupEventHandlers(): void {
    this.io.on('connection', (socket: AuthenticatedSocket) => {
      this.handleConnection(socket);
    });

    // Setup market data event listeners
    this.marketDataManager.on('ticker', (exchange: string, ticker: any) => {
      this.broadcastToChannel('market', {
        type: 'ticker',
        exchange,
        data: ticker
      });
    });

    this.marketDataManager.on('orderbook', (exchange: string, orderBook: any) => {
      this.broadcastToChannel('market', {
        type: 'orderbook',
        exchange,
        data: orderBook
      });
    });

    // Setup portfolio event listeners
    this.portfolioTracker.on('portfolioUpdated', (portfolio: any) => {
      this.broadcastToChannel('portfolio', {
        type: 'portfolio_update',
        data: portfolio
      });
    });

    this.portfolioTracker.on('positionUpdated', (position: any) => {
      this.broadcastToChannel('positions', {
        type: 'position_update',
        data: position
      });
    });
  }

  /**
   * Handle new WebSocket connection
   */
  private handleConnection(socket: AuthenticatedSocket): void {
    this.connectedClients.set(socket.id, socket);
    
    this.logger.info('WebSocket client connected', {
      userId: socket.user?.id,
      socketId: socket.id,
      totalClients: this.connectedClients.size
    });

    // Send initial data
    this.sendInitialData(socket);

    // Handle subscription requests
    socket.on('subscribe', (request: SubscriptionRequest) => {
      this.handleSubscription(socket, request);
    });

    // Handle unsubscription requests
    socket.on('unsubscribe', (request: SubscriptionRequest) => {
      this.handleUnsubscription(socket, request);
    });

    // Handle ping/pong for connection health
    socket.on('ping', () => {
      socket.emit('pong', { timestamp: Date.now() });
    });

    // Handle disconnection
    socket.on('disconnect', (reason: string) => {
      this.handleDisconnection(socket, reason);
    });

    // Handle errors
    socket.on('error', (error: Error) => {
      this.logger.error('WebSocket client error:', error, {
        userId: socket.user?.id,
        socketId: socket.id
      });
    });
  }

  /**
   * Send initial data to newly connected client
   */
  private async sendInitialData(socket: AuthenticatedSocket): Promise<void> {
    try {
      // Send portfolio summary
      const portfolio = this.portfolioTracker.getPortfolioSummary();
      if (portfolio) {
        socket.emit('initial_data', {
          type: 'portfolio',
          data: portfolio,
          timestamp: Date.now()
        });
      }

      // Send current positions
      const positions = this.portfolioTracker.getAllPositions();
      socket.emit('initial_data', {
        type: 'positions',
        data: positions,
        timestamp: Date.now()
      });

      // Send system status
      socket.emit('initial_data', {
        type: 'system_status',
        data: {
          connected: true,
          timestamp: Date.now()
        },
        timestamp: Date.now()
      });

    } catch (error) {
      this.logger.error('Failed to send initial data:', error);
    }
  }

  /**
   * Handle subscription request
   */
  private handleSubscription(socket: AuthenticatedSocket, request: SubscriptionRequest): void {
    try {
      const { channel, symbol, interval } = request;
      
      // Validate subscription request
      if (!this.isValidChannel(channel)) {
        socket.emit('error', {
          type: 'invalid_channel',
          message: `Invalid channel: ${channel}`,
          timestamp: Date.now()
        });
        return;
      }

      // Check permissions
      if (!this.hasChannelPermission(socket, channel)) {
        socket.emit('error', {
          type: 'permission_denied',
          message: `No permission for channel: ${channel}`,
          timestamp: Date.now()
        });
        return;
      }

      // Add to subscriptions
      const subscriptionKey = symbol ? `${channel}:${symbol}` : channel;
      socket.subscriptions?.add(subscriptionKey);

      if (!this.subscriptions.has(subscriptionKey)) {
        this.subscriptions.set(subscriptionKey, new Set());
      }
      this.subscriptions.get(subscriptionKey)!.add(socket.id);

      socket.emit('subscribed', {
        channel,
        symbol,
        interval,
        timestamp: Date.now()
      });

      this.logger.debug('Client subscribed to channel', {
        userId: socket.user?.id,
        socketId: socket.id,
        channel: subscriptionKey
      });

    } catch (error) {
      this.logger.error('Subscription error:', error);
      socket.emit('error', {
        type: 'subscription_error',
        message: 'Failed to subscribe to channel',
        timestamp: Date.now()
      });
    }
  }

  /**
   * Handle unsubscription request
   */
  private handleUnsubscription(socket: AuthenticatedSocket, request: SubscriptionRequest): void {
    try {
      const { channel, symbol } = request;
      const subscriptionKey = symbol ? `${channel}:${symbol}` : channel;

      // Remove from subscriptions
      socket.subscriptions?.delete(subscriptionKey);
      this.subscriptions.get(subscriptionKey)?.delete(socket.id);

      // Clean up empty subscription sets
      if (this.subscriptions.get(subscriptionKey)?.size === 0) {
        this.subscriptions.delete(subscriptionKey);
      }

      socket.emit('unsubscribed', {
        channel,
        symbol,
        timestamp: Date.now()
      });

      this.logger.debug('Client unsubscribed from channel', {
        userId: socket.user?.id,
        socketId: socket.id,
        channel: subscriptionKey
      });

    } catch (error) {
      this.logger.error('Unsubscription error:', error);
    }
  }

  /**
   * Handle client disconnection
   */
  private handleDisconnection(socket: AuthenticatedSocket, reason: string): void {
    // Clean up subscriptions
    if (socket.subscriptions) {
      for (const subscriptionKey of socket.subscriptions) {
        this.subscriptions.get(subscriptionKey)?.delete(socket.id);
        
        // Clean up empty subscription sets
        if (this.subscriptions.get(subscriptionKey)?.size === 0) {
          this.subscriptions.delete(subscriptionKey);
        }
      }
    }

    // Remove from connected clients
    this.connectedClients.delete(socket.id);

    this.logger.info('WebSocket client disconnected', {
      userId: socket.user?.id,
      socketId: socket.id,
      reason,
      totalClients: this.connectedClients.size
    });
  }

  /**
   * Broadcast message to all subscribers of a channel
   */
  private broadcastToChannel(channel: string, message: WebSocketMessage): void {
    const subscribers = this.subscriptions.get(channel);
    if (!subscribers || subscribers.size === 0) {
      return;
    }

    const messageWithTimestamp = {
      ...message,
      timestamp: Date.now()
    };

    for (const socketId of subscribers) {
      const socket = this.connectedClients.get(socketId);
      if (socket) {
        socket.emit('update', messageWithTimestamp);
      }
    }
  }

  /**
   * Start periodic updates
   */
  private startPeriodicUpdates(): void {
    // Portfolio updates
    const portfolioInterval = setInterval(() => {
      const portfolio = this.portfolioTracker.getPortfolioSummary();
      if (portfolio) {
        this.broadcastToChannel('portfolio', {
          type: 'portfolio_update',
          data: portfolio,
          timestamp: Date.now()
        });
      }
    }, this.UPDATE_INTERVALS.portfolio);

    this.updateIntervals.set('portfolio', portfolioInterval);

    // System health updates
    const healthInterval = setInterval(() => {
      const health = {
        connectedClients: this.connectedClients.size,
        activeSubscriptions: this.subscriptions.size,
        uptime: process.uptime(),
        memory: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        timestamp: Date.now()
      };

      this.broadcastToChannel('system', {
        type: 'health_update',
        data: health,
        timestamp: Date.now()
      });
    }, this.UPDATE_INTERVALS.risk);

    this.updateIntervals.set('health', healthInterval);
  }

  /**
   * Validate channel name
   */
  private isValidChannel(channel: string): boolean {
    const validChannels = [
      'portfolio', 'positions', 'orders', 'market', 'risk', 'system', 'trades'
    ];
    return validChannels.includes(channel);
  }

  /**
   * Check if user has permission for channel
   */
  private hasChannelPermission(socket: AuthenticatedSocket, channel: string): boolean {
    const user = socket.user;
    if (!user) return false;

    // Basic permission check - in a real implementation, this would be more sophisticated
    const requiredPermissions: { [key: string]: string[] } = {
      portfolio: ['view_portfolio'],
      positions: ['view_portfolio'],
      orders: ['view_orders'],
      market: ['view_market'],
      risk: ['view_risk'],
      system: ['view_system'],
      trades: ['view_trades']
    };

    const required = requiredPermissions[channel] || [];
    return required.every(perm => user.permissions.includes(perm)) || user.permissions.includes('admin');
  }

  /**
   * Get connection statistics
   */
  public getStatistics(): {
    connectedClients: number;
    activeSubscriptions: number;
    messagesSent: number;
    uptime: number;
  } {
    return {
      connectedClients: this.connectedClients.size,
      activeSubscriptions: this.subscriptions.size,
      messagesSent: 0, // Would track in real implementation
      uptime: process.uptime()
    };
  }

  /**
   * Broadcast system-wide message
   */
  public broadcastSystemMessage(message: any): void {
    this.io.emit('system_message', {
      ...message,
      timestamp: Date.now()
    });
  }

  /**
   * Send message to specific user
   */
  public sendToUser(userId: string, message: any): void {
    for (const socket of this.connectedClients.values()) {
      if (socket.user?.id === userId) {
        socket.emit('user_message', {
          ...message,
          timestamp: Date.now()
        });
      }
    }
  }

  /**
   * Health check
   */
  public async healthCheck(): Promise<{ status: 'healthy' | 'unhealthy'; clients: number; subscriptions: number }> {
    return {
      status: 'healthy',
      clients: this.connectedClients.size,
      subscriptions: this.subscriptions.size
    };
  }

  /**
   * Close WebSocket server
   */
  public async close(): Promise<void> {
    // Clear intervals
    for (const interval of this.updateIntervals.values()) {
      clearInterval(interval);
    }
    this.updateIntervals.clear();

    // Disconnect all clients
    for (const socket of this.connectedClients.values()) {
      socket.disconnect(true);
    }

    this.connectedClients.clear();
    this.subscriptions.clear();

    this.logger.info('WebSocket server closed');
  }
}
