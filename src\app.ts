/**
 * AlphaMarketMaker - Main Application Entry Point
 * 
 * High-performance cryptocurrency market making bot with multi-exchange support,
 * real-time liquidity provision, and advanced risk management.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import rateLimit from 'express-rate-limit';

import { Config } from '@/utils/Config';
import { Logger } from '@/utils/Logger';
import { DatabaseManager } from '@/data/DatabaseManager';
import { CacheManager } from '@/data/CacheManager';
import { MarketDataManager } from '@/data/MarketDataManager';
import { MarketMaker } from '@/core/MarketMaker';
import { RiskManager } from '@/core/RiskManager';
import { OrderManager } from '@/core/OrderManager';
import { PortfolioTracker } from '@/core/PortfolioTracker';
import { RestAPI } from '@/api/RestAPI';
import { WebSocketServer } from '@/api/WebSocketServer';
import { RateLimiter } from '@/api/RateLimiter';

class AlphaMarketMakerApp {
  private app: express.Application;
  private server: any;
  private io: SocketIOServer;
  private config: Config;
  private logger: Logger;
  private dbManager: DatabaseManager;
  private cacheManager: CacheManager;
  private marketDataManager: MarketDataManager;
  private marketMaker: MarketMaker;
  private riskManager: RiskManager;
  private orderManager: OrderManager;
  private portfolioTracker: PortfolioTracker;
  private rateLimiter: RateLimiter;
  private restAPI: RestAPI;
  private wsServer: WebSocketServer;

  constructor() {
    this.app = express();
    this.server = createServer(this.app);
    this.io = new SocketIOServer(this.server, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:3000",
        methods: ["GET", "POST"]
      }
    });

    this.initializeConfig();
    this.initializeLogger();
    this.setupMiddleware();
    this.setupRateLimiting();
  }

  /**
   * Initialize application configuration
   */
  private initializeConfig(): void {
    this.config = new Config();
    
    // Validate required environment variables
    const requiredEnvVars = [
      'BINANCE_API_KEY',
      'BINANCE_SECRET_KEY',
      'COINBASE_API_KEY',
      'COINBASE_SECRET',
      'KRAKEN_API_KEY',
      'KRAKEN_SECRET',
      'DATABASE_URL',
      'JWT_SECRET'
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    if (missingVars.length > 0) {
      console.error(`Missing required environment variables: ${missingVars.join(', ')}`);
      process.exit(1);
    }
  }

  /**
   * Initialize structured logging
   */
  private initializeLogger(): void {
    this.logger = new Logger({
      level: process.env.LOG_LEVEL || 'info',
      service: 'alphamarketmaker',
      environment: process.env.NODE_ENV || 'development'
    });
  }

  /**
   * Setup Express middleware
   */
  private setupMiddleware(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'", "wss:", "ws:"]
        }
      }
    }));

    // CORS configuration
    this.app.use(cors({
      origin: process.env.FRONTEND_URL || "http://localhost:3000",
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization']
    }));

    // Compression and parsing
    this.app.use(compression());
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request logging
    this.app.use(morgan('combined', {
      stream: {
        write: (message: string) => this.logger.info(message.trim())
      }
    }));
  }

  /**
   * Setup rate limiting for API endpoints
   */
  private setupRateLimiting(): void {
    // General API rate limiting
    const generalLimiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 1000, // limit each IP to 1000 requests per windowMs
      message: {
        error: 'Too many requests from this IP, please try again later.',
        retryAfter: '15 minutes'
      },
      standardHeaders: true,
      legacyHeaders: false,
    });

    // Strict rate limiting for trading endpoints
    const tradingLimiter = rateLimit({
      windowMs: 1 * 60 * 1000, // 1 minute
      max: 100, // limit each IP to 100 trading requests per minute
      message: {
        error: 'Trading rate limit exceeded, please slow down.',
        retryAfter: '1 minute'
      }
    });

    this.app.use('/api/', generalLimiter);
    this.app.use('/api/v1/start', tradingLimiter);
    this.app.use('/api/v1/stop', tradingLimiter);
    this.app.use('/api/v1/orders', tradingLimiter);
  }

  /**
   * Initialize all core services
   */
  private async initializeServices(): Promise<void> {
    try {
      this.logger.info('Initializing AlphaMarketMaker services...');

      // Initialize data layer
      this.dbManager = new DatabaseManager(this.config, this.logger);
      await this.dbManager.initialize();

      this.cacheManager = new CacheManager(this.config, this.logger);
      await this.cacheManager.initialize();

      // Initialize market data manager
      this.marketDataManager = new MarketDataManager(this.config, this.logger, this.cacheManager);
      await this.marketDataManager.initialize();

      // Initialize core trading components
      this.riskManager = new RiskManager(this.config, this.logger, this.dbManager);
      this.orderManager = new OrderManager(this.config, this.logger, this.dbManager);
      this.portfolioTracker = new PortfolioTracker(this.config, this.logger, this.dbManager);

      // Initialize market maker engine
      this.marketMaker = new MarketMaker(
        this.config,
        this.logger,
        this.marketDataManager,
        this.riskManager,
        this.orderManager,
        this.portfolioTracker
      );

      // Initialize rate limiter
      this.rateLimiter = new RateLimiter(this.logger, this.cacheManager);

      // Initialize API services
      this.restAPI = new RestAPI(
        this.app,
        this.config,
        this.logger,
        this.marketMaker,
        this.portfolioTracker,
        this.riskManager
      );

      // Set rate limiter for REST API
      this.restAPI.setRateLimiter(this.rateLimiter);

      this.wsServer = new WebSocketServer(
        this.io,
        this.config,
        this.logger,
        this.marketDataManager,
        this.portfolioTracker
      );

      this.logger.info('All services initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize services:', error);
      throw error;
    }
  }

  /**
   * Setup graceful shutdown handlers
   */
  private setupGracefulShutdown(): void {
    const shutdown = async (signal: string) => {
      this.logger.info(`Received ${signal}, starting graceful shutdown...`);

      try {
        // Stop market making activities
        if (this.marketMaker) {
          await this.marketMaker.stop();
        }

        // Close WebSocket connections
        if (this.wsServer) {
          await this.wsServer.close();
        }

        // Close database connections
        if (this.dbManager) {
          await this.dbManager.close();
        }

        // Close cache connections
        if (this.cacheManager) {
          await this.cacheManager.close();
        }

        // Close HTTP server
        this.server.close(() => {
          this.logger.info('Graceful shutdown completed');
          process.exit(0);
        });

        // Force exit after 30 seconds
        setTimeout(() => {
          this.logger.error('Forced shutdown after timeout');
          process.exit(1);
        }, 30000);

      } catch (error) {
        this.logger.error('Error during shutdown:', error);
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGUSR2', () => shutdown('SIGUSR2')); // nodemon restart
  }

  /**
   * Start the application
   */
  public async start(): Promise<void> {
    try {
      await this.initializeServices();
      this.setupGracefulShutdown();

      const port = process.env.PORT || 8080;
      
      this.server.listen(port, () => {
        this.logger.info(`AlphaMarketMaker server started on port ${port}`);
        this.logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
        this.logger.info(`WebSocket server ready for connections`);
        this.logger.info(`Health check available at http://localhost:${port}/api/v1/health`);
      });

    } catch (error) {
      this.logger.error('Failed to start application:', error);
      process.exit(1);
    }
  }
}

// Start the application if this file is run directly
if (require.main === module) {
  const app = new AlphaMarketMakerApp();
  app.start().catch((error) => {
    console.error('Failed to start AlphaMarketMaker:', error);
    process.exit(1);
  });
}

export default AlphaMarketMakerApp;
