/**
 * Main Application Component for AlphaMarketMaker Dashboard
 * 
 * Root component with routing, authentication, and layout management
 * for the cryptocurrency market making trading dashboard.
 * 
 * Git Commit: "feat: add main App component with routing and authentication"
 */

import React, { Suspense, useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from './hooks/useAuth';
import { useWebSocket } from './hooks/useWebSocket';

// Layout Components
import Layout from './components/Layout/Layout';
import LoadingSpinner from './components/UI/LoadingSpinner';
import ErrorBoundary from './components/ErrorBoundary';

// Page Components (Lazy loaded for better performance)
const Dashboard = React.lazy(() => import('./pages/Dashboard'));
const Portfolio = React.lazy(() => import('./pages/Portfolio'));
const Trading = React.lazy(() => import('./pages/Trading'));
const RiskManagement = React.lazy(() => import('./pages/RiskManagement'));
const Analytics = React.lazy(() => import('./pages/Analytics'));
const Settings = React.lazy(() => import('./pages/Settings'));
const Login = React.lazy(() => import('./pages/Login'));

// Protected Route Component
interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-dark-900">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
};

// Main App Component
const App: React.FC = () => {
  const { isAuthenticated, user } = useAuth();
  const { connectionStatus, connect, disconnect } = useWebSocket();

  // Connect to WebSocket when authenticated
  useEffect(() => {
    if (isAuthenticated && user?.token) {
      connect(user.token);
    } else {
      disconnect();
    }

    return () => {
      disconnect();
    };
  }, [isAuthenticated, user?.token, connect, disconnect]);

  // Handle connection status changes
  useEffect(() => {
    if (connectionStatus === 'connected') {
      console.log('WebSocket connected successfully');
    } else if (connectionStatus === 'disconnected') {
      console.log('WebSocket disconnected');
    } else if (connectionStatus === 'error') {
      console.error('WebSocket connection error');
    }
  }, [connectionStatus]);

  return (
    <div className="App min-h-screen bg-dark-900 text-dark-50">
      <ErrorBoundary>
        <Routes>
          {/* Public Routes */}
          <Route
            path="/login"
            element={
              <Suspense fallback={<LoadingSpinner size="lg" />}>
                {isAuthenticated ? <Navigate to="/dashboard" replace /> : <Login />}
              </Suspense>
            }
          />

          {/* Protected Routes */}
          <Route
            path="/*"
            element={
              <ProtectedRoute>
                <Layout>
                  <Suspense
                    fallback={
                      <div className="flex items-center justify-center h-64">
                        <LoadingSpinner size="lg" />
                      </div>
                    }
                  >
                    <Routes>
                      {/* Dashboard Routes */}
                      <Route path="/dashboard" element={<Dashboard />} />
                      <Route path="/portfolio" element={<Portfolio />} />
                      <Route path="/trading" element={<Trading />} />
                      <Route path="/risk" element={<RiskManagement />} />
                      <Route path="/analytics" element={<Analytics />} />
                      <Route path="/settings" element={<Settings />} />

                      {/* Default redirect */}
                      <Route path="/" element={<Navigate to="/dashboard" replace />} />
                      
                      {/* 404 Route */}
                      <Route
                        path="*"
                        element={
                          <div className="flex flex-col items-center justify-center h-64 text-center">
                            <h1 className="text-4xl font-bold text-dark-200 mb-4">404</h1>
                            <p className="text-dark-400 mb-6">Page not found</p>
                            <button
                              onClick={() => window.history.back()}
                              className="btn-primary"
                            >
                              Go Back
                            </button>
                          </div>
                        }
                      />
                    </Routes>
                  </Suspense>
                </Layout>
              </ProtectedRoute>
            }
          />
        </Routes>
      </ErrorBoundary>

      {/* Connection Status Indicator */}
      {isAuthenticated && (
        <div className="fixed bottom-4 right-4 z-50">
          <div
            className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
              connectionStatus === 'connected'
                ? 'bg-profit-600 text-white'
                : connectionStatus === 'connecting'
                ? 'bg-yellow-600 text-white'
                : connectionStatus === 'error'
                ? 'bg-loss-600 text-white'
                : 'bg-dark-700 text-dark-300'
            }`}
          >
            <div
              className={`w-2 h-2 rounded-full ${
                connectionStatus === 'connected'
                  ? 'bg-white animate-pulse'
                  : connectionStatus === 'connecting'
                  ? 'bg-white animate-spin'
                  : connectionStatus === 'error'
                  ? 'bg-white'
                  : 'bg-dark-500'
              }`}
            />
            <span>
              {connectionStatus === 'connected'
                ? 'Connected'
                : connectionStatus === 'connecting'
                ? 'Connecting...'
                : connectionStatus === 'error'
                ? 'Connection Error'
                : 'Disconnected'}
            </span>
          </div>
        </div>
      )}

      {/* Performance Monitor (Development Only) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 left-4 z-50">
          <div className="bg-dark-800 border border-dark-600 rounded-lg p-2 text-xs text-dark-400">
            <div>Memory: {Math.round(performance.memory?.usedJSHeapSize / 1024 / 1024 || 0)}MB</div>
            <div>Connection: {connectionStatus}</div>
            <div>User: {user?.email || 'Not logged in'}</div>
          </div>
        </div>
      )}
    </div>
  );
};

export default App;
