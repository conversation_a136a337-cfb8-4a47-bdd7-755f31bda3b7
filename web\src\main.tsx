/**
 * Main Application Entry Point for AlphaMarketMaker Dashboard
 * 
 * React application initialization with routing, error boundaries,
 * and performance monitoring for the trading dashboard.
 * 
 * Git Commit: "feat: add React application entry point with routing and error handling"
 */

import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';

import App from './App';
import ErrorBoundary from './components/ErrorBoundary';
import { AuthProvider } from './contexts/AuthContext';
import { WebSocketProvider } from './contexts/WebSocketContext';
import { ThemeProvider } from './contexts/ThemeContext';

import './index.css';

// Performance monitoring
const startTime = performance.now();

// Error reporting
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error);
  // In production, send to error reporting service
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
  // In production, send to error reporting service
});

// Initialize React application
const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <ErrorBoundary>
      <BrowserRouter>
        <ThemeProvider>
          <AuthProvider>
            <WebSocketProvider>
              <App />
              
              {/* Global toast notifications */}
              <Toaster
                position="top-right"
                toastOptions={{
                  duration: 4000,
                  style: {
                    background: '#1e293b',
                    color: '#f1f5f9',
                    border: '1px solid #334155',
                    borderRadius: '0.5rem',
                    fontSize: '0.875rem'
                  },
                  success: {
                    iconTheme: {
                      primary: '#22c55e',
                      secondary: '#f1f5f9'
                    }
                  },
                  error: {
                    iconTheme: {
                      primary: '#ef4444',
                      secondary: '#f1f5f9'
                    }
                  }
                }}
              />
            </WebSocketProvider>
          </AuthProvider>
        </ThemeProvider>
      </BrowserRouter>
    </ErrorBoundary>
  </React.StrictMode>
);

// Performance measurement
window.addEventListener('load', () => {
  const loadTime = performance.now() - startTime;
  console.log(`App loaded in ${loadTime.toFixed(2)}ms`);
  
  // Hide loading screen
  const loadingScreen = document.getElementById('loading-screen');
  if (loadingScreen) {
    loadingScreen.style.opacity = '0';
    loadingScreen.style.transition = 'opacity 0.3s ease-out';
    setTimeout(() => {
      loadingScreen.remove();
      document.body.classList.add('app-loaded');
    }, 300);
  }
  
  // Report performance metrics
  if ('performance' in window && 'getEntriesByType' in performance) {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const paint = performance.getEntriesByType('paint');
    
    const metrics = {
      loadTime,
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
      firstPaint: paint.find(p => p.name === 'first-paint')?.startTime || 0,
      firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0
    };
    
    console.log('Performance metrics:', metrics);
    
    // In production, send metrics to analytics service
    // analytics.track('app_performance', metrics);
  }
});

// Hot module replacement for development
if (import.meta.hot) {
  import.meta.hot.accept();
}
