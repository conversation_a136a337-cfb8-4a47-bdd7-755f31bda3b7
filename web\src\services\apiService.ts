/**
 * API Service for AlphaMarketMaker Dashboard
 * 
 * Centralized HTTP client with authentication, error handling,
 * request/response interceptors, and retry logic.
 * 
 * Git Commit: "feat: add API service with authentication and error handling"
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import toast from 'react-hot-toast';

// API Response interface
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: number;
  requestId: string;
}

// API Error interface
export interface APIError {
  message: string;
  status: number;
  code?: string;
  details?: any;
}

// Request configuration
interface RequestConfig extends AxiosRequestConfig {
  skipAuth?: boolean;
  skipErrorToast?: boolean;
  retries?: number;
}

class APIService {
  private client: AxiosInstance;
  private authToken: string | null = null;
  private baseURL: string;

  constructor() {
    this.baseURL = process.env.NODE_ENV === 'production' 
      ? 'https://api.alphamarketmaker.com/api/v1'
      : 'http://localhost:8080/api/v1';

    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    this.setupInterceptors();
  }

  /**
   * Setup request and response interceptors
   */
  private setupInterceptors(): void {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        // Add authentication token
        if (this.authToken && !config.skipAuth) {
          config.headers.Authorization = `Bearer ${this.authToken}`;
        }

        // Add request timestamp
        config.metadata = { startTime: Date.now() };

        // Log request in development
        if (process.env.NODE_ENV === 'development') {
          console.log(`🚀 ${config.method?.toUpperCase()} ${config.url}`, {
            data: config.data,
            params: config.params
          });
        }

        return config;
      },
      (error) => {
        console.error('Request interceptor error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        // Calculate request duration
        const duration = Date.now() - (response.config.metadata?.startTime || 0);

        // Log response in development
        if (process.env.NODE_ENV === 'development') {
          console.log(`✅ ${response.config.method?.toUpperCase()} ${response.config.url} (${duration}ms)`, {
            status: response.status,
            data: response.data
          });
        }

        return response;
      },
      async (error: AxiosError) => {
        const config = error.config as RequestConfig;
        const duration = Date.now() - (config?.metadata?.startTime || 0);

        // Log error in development
        if (process.env.NODE_ENV === 'development') {
          console.error(`❌ ${config?.method?.toUpperCase()} ${config?.url} (${duration}ms)`, {
            status: error.response?.status,
            data: error.response?.data,
            message: error.message
          });
        }

        // Handle specific error cases
        if (error.response?.status === 401) {
          // Authentication error
          this.handleAuthError();
          return Promise.reject(this.createAPIError('Authentication failed', 401));
        }

        if (error.response?.status === 429) {
          // Rate limit error
          const retryAfter = error.response.headers['retry-after'];
          const message = `Rate limit exceeded${retryAfter ? `. Retry after ${retryAfter} seconds` : ''}`;
          
          if (!config?.skipErrorToast) {
            toast.error(message);
          }
          
          return Promise.reject(this.createAPIError(message, 429));
        }

        // Retry logic for network errors
        if (this.shouldRetry(error, config)) {
          return this.retryRequest(config);
        }

        // Handle other errors
        const apiError = this.handleError(error, config);
        return Promise.reject(apiError);
      }
    );
  }

  /**
   * Check if request should be retried
   */
  private shouldRetry(error: AxiosError, config?: RequestConfig): boolean {
    if (!config || config.retries === 0) return false;
    
    // Retry on network errors or 5xx server errors
    return !error.response || (error.response.status >= 500 && error.response.status < 600);
  }

  /**
   * Retry failed request
   */
  private async retryRequest(config: RequestConfig): Promise<AxiosResponse> {
    const retries = (config.retries || 3) - 1;
    const delay = Math.min(1000 * Math.pow(2, 3 - retries), 5000); // Exponential backoff

    console.log(`Retrying request in ${delay}ms (${retries} retries left)`);

    await new Promise(resolve => setTimeout(resolve, delay));

    return this.client({
      ...config,
      retries
    });
  }

  /**
   * Handle authentication errors
   */
  private handleAuthError(): void {
    this.authToken = null;
    
    // Dispatch custom event for auth context to handle
    window.dispatchEvent(new CustomEvent('auth-error'));
  }

  /**
   * Handle API errors
   */
  private handleError(error: AxiosError, config?: RequestConfig): APIError {
    let message = 'An unexpected error occurred';
    let status = 0;
    let code: string | undefined;
    let details: any;

    if (error.response) {
      // Server responded with error status
      status = error.response.status;
      const responseData = error.response.data as any;
      
      if (responseData?.error) {
        message = responseData.error;
      } else if (responseData?.message) {
        message = responseData.message;
      } else {
        message = `Server error (${status})`;
      }
      
      code = responseData?.code;
      details = responseData?.details;
    } else if (error.request) {
      // Network error
      message = 'Network error - please check your connection';
      status = 0;
    } else {
      // Request setup error
      message = error.message || 'Request failed';
    }

    // Show error toast unless disabled
    if (!config?.skipErrorToast) {
      toast.error(message);
    }

    return this.createAPIError(message, status, code, details);
  }

  /**
   * Create standardized API error
   */
  private createAPIError(message: string, status: number, code?: string, details?: any): APIError {
    return {
      message,
      status,
      code,
      details
    };
  }

  /**
   * Set authentication token
   */
  public setAuthToken(token: string): void {
    this.authToken = token;
  }

  /**
   * Clear authentication token
   */
  public clearAuthToken(): void {
    this.authToken = null;
  }

  /**
   * GET request
   */
  public async get<T = any>(url: string, config?: RequestConfig): Promise<APIResponse<T>> {
    try {
      const response = await this.client.get<APIResponse<T>>(url, config);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * POST request
   */
  public async post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<APIResponse<T>> {
    try {
      const response = await this.client.post<APIResponse<T>>(url, data, config);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * PUT request
   */
  public async put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<APIResponse<T>> {
    try {
      const response = await this.client.put<APIResponse<T>>(url, data, config);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * PATCH request
   */
  public async patch<T = any>(url: string, data?: any, config?: RequestConfig): Promise<APIResponse<T>> {
    try {
      const response = await this.client.patch<APIResponse<T>>(url, data, config);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * DELETE request
   */
  public async delete<T = any>(url: string, config?: RequestConfig): Promise<APIResponse<T>> {
    try {
      const response = await this.client.delete<APIResponse<T>>(url, config);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Upload file
   */
  public async upload<T = any>(url: string, file: File, config?: RequestConfig): Promise<APIResponse<T>> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await this.client.post<APIResponse<T>>(url, formData, {
        ...config,
        headers: {
          ...config?.headers,
          'Content-Type': 'multipart/form-data'
        }
      });

      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Download file
   */
  public async download(url: string, filename?: string, config?: RequestConfig): Promise<void> {
    try {
      const response = await this.client.get(url, {
        ...config,
        responseType: 'blob'
      });

      // Create download link
      const blob = new Blob([response.data]);
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename || 'download';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Health check
   */
  public async healthCheck(): Promise<boolean> {
    try {
      const response = await this.get('/health', { 
        skipAuth: true, 
        skipErrorToast: true,
        timeout: 5000
      });
      return response.success;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get base URL
   */
  public getBaseURL(): string {
    return this.baseURL;
  }

  /**
   * Get current auth token
   */
  public getAuthToken(): string | null {
    return this.authToken;
  }
}

// Create and export singleton instance
export const apiService = new APIService();

// Export types
export type { APIResponse, APIError, RequestConfig };
