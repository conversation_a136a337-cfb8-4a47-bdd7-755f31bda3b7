/**
 * WebSocket Hook for AlphaMarketMaker Dashboard
 * 
 * Custom React hook for managing WebSocket connections with automatic
 * reconnection, subscription management, and real-time data handling.
 * 
 * Git Commit: "feat: add WebSocket hook for real-time market data and updates"
 */

import { useEffect, useRef, useCallback, useState } from 'react';
import { io, Socket } from 'socket.io-client';
import toast from 'react-hot-toast';

export type ConnectionStatus = 'disconnected' | 'connecting' | 'connected' | 'error';

export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: number;
  requestId?: string;
}

export interface SubscriptionRequest {
  channel: string;
  symbol?: string;
  interval?: string;
}

export interface WebSocketHook {
  socket: Socket | null;
  connectionStatus: ConnectionStatus;
  connect: (token: string) => void;
  disconnect: () => void;
  subscribe: (request: SubscriptionRequest) => void;
  unsubscribe: (request: SubscriptionRequest) => void;
  sendMessage: (type: string, data: any) => void;
  isConnected: boolean;
}

// WebSocket configuration
const WEBSOCKET_CONFIG = {
  url: process.env.NODE_ENV === 'production' 
    ? 'wss://api.alphamarketmaker.com' 
    : 'ws://localhost:8080',
  options: {
    transports: ['websocket'],
    upgrade: true,
    rememberUpgrade: true,
    timeout: 10000,
    forceNew: true,
    reconnection: true,
    reconnectionAttempts: 5,
    reconnectionDelay: 1000,
    reconnectionDelayMax: 5000,
    maxReconnectionAttempts: 5
  }
};

export const useWebSocket = (): WebSocketHook => {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('disconnected');
  const socketRef = useRef<Socket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const subscriptionsRef = useRef<Set<string>>(new Set());
  const reconnectAttemptsRef = useRef(0);

  // Clear reconnection timeout
  const clearReconnectTimeout = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
  }, []);

  // Handle connection events
  const setupEventListeners = useCallback((socket: Socket) => {
    socket.on('connect', () => {
      console.log('WebSocket connected');
      setConnectionStatus('connected');
      reconnectAttemptsRef.current = 0;
      clearReconnectTimeout();
      
      // Resubscribe to previous subscriptions
      subscriptionsRef.current.forEach(subscription => {
        const [channel, symbol] = subscription.split(':');
        socket.emit('subscribe', { channel, symbol });
      });
      
      toast.success('Connected to real-time data');
    });

    socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
      setConnectionStatus('disconnected');
      
      if (reason === 'io server disconnect') {
        // Server initiated disconnect, don't reconnect automatically
        toast.error('Disconnected from server');
      } else {
        // Client initiated or network issue, attempt reconnect
        toast.error('Connection lost, attempting to reconnect...');
        attemptReconnect();
      }
    });

    socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      setConnectionStatus('error');
      toast.error('Connection error');
      attemptReconnect();
    });

    socket.on('error', (error) => {
      console.error('WebSocket error:', error);
      toast.error(`WebSocket error: ${error.message}`);
    });

    // Handle authentication errors
    socket.on('unauthorized', (error) => {
      console.error('WebSocket authentication failed:', error);
      setConnectionStatus('error');
      toast.error('Authentication failed');
      // Redirect to login or refresh token
    });

    // Handle subscription confirmations
    socket.on('subscribed', (data) => {
      console.log('Subscribed to channel:', data);
      const subscriptionKey = data.symbol ? `${data.channel}:${data.symbol}` : data.channel;
      subscriptionsRef.current.add(subscriptionKey);
    });

    socket.on('unsubscribed', (data) => {
      console.log('Unsubscribed from channel:', data);
      const subscriptionKey = data.symbol ? `${data.channel}:${data.symbol}` : data.channel;
      subscriptionsRef.current.delete(subscriptionKey);
    });

    // Handle ping/pong for connection health
    socket.on('pong', (data) => {
      console.log('Pong received:', data);
    });

    // Handle initial data
    socket.on('initial_data', (message: WebSocketMessage) => {
      console.log('Initial data received:', message.type);
      // Emit custom event for components to listen to
      window.dispatchEvent(new CustomEvent('websocket-initial-data', { 
        detail: message 
      }));
    });

    // Handle real-time updates
    socket.on('update', (message: WebSocketMessage) => {
      console.log('Update received:', message.type);
      // Emit custom event for components to listen to
      window.dispatchEvent(new CustomEvent('websocket-update', { 
        detail: message 
      }));
    });

    // Handle system messages
    socket.on('system_message', (message: any) => {
      console.log('System message:', message);
      toast(message.message, { 
        icon: message.type === 'warning' ? '⚠️' : 'ℹ️' 
      });
    });

    // Handle user-specific messages
    socket.on('user_message', (message: any) => {
      console.log('User message:', message);
      toast(message.message, { 
        icon: message.type === 'success' ? '✅' : message.type === 'error' ? '❌' : 'ℹ️' 
      });
    });
  }, [clearReconnectTimeout]);

  // Attempt reconnection with exponential backoff
  const attemptReconnect = useCallback(() => {
    if (reconnectAttemptsRef.current >= WEBSOCKET_CONFIG.options.maxReconnectionAttempts) {
      console.log('Max reconnection attempts reached');
      setConnectionStatus('error');
      toast.error('Unable to reconnect. Please refresh the page.');
      return;
    }

    const delay = Math.min(
      WEBSOCKET_CONFIG.options.reconnectionDelay * Math.pow(2, reconnectAttemptsRef.current),
      WEBSOCKET_CONFIG.options.reconnectionDelayMax
    );

    console.log(`Attempting reconnection in ${delay}ms (attempt ${reconnectAttemptsRef.current + 1})`);
    
    reconnectTimeoutRef.current = setTimeout(() => {
      reconnectAttemptsRef.current++;
      setConnectionStatus('connecting');
      
      if (socketRef.current) {
        socketRef.current.connect();
      }
    }, delay);
  }, []);

  // Connect to WebSocket
  const connect = useCallback((token: string) => {
    if (socketRef.current?.connected) {
      console.log('WebSocket already connected');
      return;
    }

    console.log('Connecting to WebSocket...');
    setConnectionStatus('connecting');

    try {
      const socket = io(WEBSOCKET_CONFIG.url, {
        ...WEBSOCKET_CONFIG.options,
        auth: {
          token
        }
      });

      socketRef.current = socket;
      setupEventListeners(socket);
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      setConnectionStatus('error');
      toast.error('Failed to establish connection');
    }
  }, [setupEventListeners]);

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    console.log('Disconnecting WebSocket...');
    
    clearReconnectTimeout();
    subscriptionsRef.current.clear();
    
    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
    }
    
    setConnectionStatus('disconnected');
  }, [clearReconnectTimeout]);

  // Subscribe to a channel
  const subscribe = useCallback((request: SubscriptionRequest) => {
    if (!socketRef.current?.connected) {
      console.warn('Cannot subscribe: WebSocket not connected');
      return;
    }

    console.log('Subscribing to channel:', request);
    socketRef.current.emit('subscribe', request);
  }, []);

  // Unsubscribe from a channel
  const unsubscribe = useCallback((request: SubscriptionRequest) => {
    if (!socketRef.current?.connected) {
      console.warn('Cannot unsubscribe: WebSocket not connected');
      return;
    }

    console.log('Unsubscribing from channel:', request);
    socketRef.current.emit('unsubscribe', request);
  }, []);

  // Send a message
  const sendMessage = useCallback((type: string, data: any) => {
    if (!socketRef.current?.connected) {
      console.warn('Cannot send message: WebSocket not connected');
      return;
    }

    console.log('Sending message:', type, data);
    socketRef.current.emit(type, data);
  }, []);

  // Send periodic ping to maintain connection
  useEffect(() => {
    if (connectionStatus === 'connected' && socketRef.current) {
      const pingInterval = setInterval(() => {
        if (socketRef.current?.connected) {
          socketRef.current.emit('ping');
        }
      }, 30000); // Ping every 30 seconds

      return () => clearInterval(pingInterval);
    }
  }, [connectionStatus]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    socket: socketRef.current,
    connectionStatus,
    connect,
    disconnect,
    subscribe,
    unsubscribe,
    sendMessage,
    isConnected: connectionStatus === 'connected'
  };
};
