/**
 * Input Validation and Sanitization Utilities for AlphaMarketMaker
 * 
 * Comprehensive validation functions for API inputs, trading parameters,
 * and security-focused sanitization to prevent injection attacks.
 */

import { body, param, query, ValidationChain } from 'express-validator';
import Big from 'big.js';

export class Validators {
  
  /**
   * Validate trading symbol format (e.g., BTC/USD, ETH/USDT)
   */
  public static symbol(): ValidationChain {
    return body('symbol')
      .isString()
      .matches(/^[A-Z]{2,10}\/[A-Z]{2,10}$/)
      .withMessage('Symbol must be in format BASE/QUOTE (e.g., BTC/USD)')
      .customSanitizer((value: string) => value.toUpperCase().trim());
  }

  /**
   * Validate exchange name
   */
  public static exchange(): ValidationChain {
    return body('exchange')
      .isString()
      .isIn(['binance', 'coinbase', 'kraken'])
      .withMessage('Exchange must be one of: binance, coinbase, kraken')
      .customSanitizer((value: string) => value.toLowerCase().trim());
  }

  /**
   * Validate trading amount (positive number with precision)
   */
  public static amount(field: string = 'amount'): ValidationChain {
    return body(field)
      .custom((value) => {
        try {
          const amount = new Big(value);
          if (amount.lte(0)) {
            throw new Error('Amount must be positive');
          }
          if (amount.gt('1000000')) {
            throw new Error('Amount too large');
          }
          return true;
        } catch (error) {
          throw new Error('Invalid amount format');
        }
      })
      .customSanitizer((value) => new Big(value).toString());
  }

  /**
   * Validate price (positive number)
   */
  public static price(field: string = 'price'): ValidationChain {
    return body(field)
      .custom((value) => {
        try {
          const price = new Big(value);
          if (price.lte(0)) {
            throw new Error('Price must be positive');
          }
          if (price.gt('10000000')) {
            throw new Error('Price too large');
          }
          return true;
        } catch (error) {
          throw new Error('Invalid price format');
        }
      })
      .customSanitizer((value) => new Big(value).toString());
  }

  /**
   * Validate spread in basis points (0-10000)
   */
  public static spreadBps(): ValidationChain {
    return body('spreadBps')
      .isInt({ min: 1, max: 10000 })
      .withMessage('Spread must be between 1 and 10000 basis points')
      .toInt();
  }

  /**
   * Validate percentage (0-100)
   */
  public static percentage(field: string): ValidationChain {
    return body(field)
      .isFloat({ min: 0, max: 100 })
      .withMessage(`${field} must be between 0 and 100`)
      .toFloat();
  }

  /**
   * Validate order side
   */
  public static orderSide(): ValidationChain {
    return body('side')
      .isString()
      .isIn(['buy', 'sell'])
      .withMessage('Side must be either buy or sell')
      .customSanitizer((value: string) => value.toLowerCase().trim());
  }

  /**
   * Validate order type
   */
  public static orderType(): ValidationChain {
    return body('type')
      .isString()
      .isIn(['market', 'limit', 'stop', 'stop-limit'])
      .withMessage('Invalid order type')
      .customSanitizer((value: string) => value.toLowerCase().trim());
  }

  /**
   * Validate UUID format
   */
  public static uuid(field: string): ValidationChain {
    return body(field)
      .isUUID()
      .withMessage(`${field} must be a valid UUID`);
  }

  /**
   * Validate order ID parameter
   */
  public static orderIdParam(): ValidationChain {
    return param('orderId')
      .isString()
      .isLength({ min: 1, max: 100 })
      .matches(/^[a-zA-Z0-9\-_]+$/)
      .withMessage('Invalid order ID format')
      .customSanitizer((value: string) => value.trim());
  }

  /**
   * Validate pagination parameters
   */
  public static pagination(): ValidationChain[] {
    return [
      query('page')
        .optional()
        .isInt({ min: 1, max: 10000 })
        .withMessage('Page must be between 1 and 10000')
        .toInt(),
      query('limit')
        .optional()
        .isInt({ min: 1, max: 1000 })
        .withMessage('Limit must be between 1 and 1000')
        .toInt()
    ];
  }

  /**
   * Validate date range
   */
  public static dateRange(): ValidationChain[] {
    return [
      query('startDate')
        .optional()
        .isISO8601()
        .withMessage('Start date must be in ISO8601 format')
        .toDate(),
      query('endDate')
        .optional()
        .isISO8601()
        .withMessage('End date must be in ISO8601 format')
        .toDate()
        .custom((endDate, { req }) => {
          if (req.query.startDate && endDate < req.query.startDate) {
            throw new Error('End date must be after start date');
          }
          return true;
        })
    ];
  }

  /**
   * Validate trading pairs array
   */
  public static tradingPairs(): ValidationChain {
    return body('pairs')
      .isArray({ min: 1, max: 20 })
      .withMessage('Pairs must be an array with 1-20 elements')
      .custom((pairs: string[]) => {
        const symbolRegex = /^[A-Z]{2,10}\/[A-Z]{2,10}$/;
        for (const pair of pairs) {
          if (!symbolRegex.test(pair)) {
            throw new Error(`Invalid symbol format: ${pair}`);
          }
        }
        return true;
      })
      .customSanitizer((pairs: string[]) => 
        pairs.map(pair => pair.toUpperCase().trim())
      );
  }

  /**
   * Validate exchanges array
   */
  public static exchanges(): ValidationChain {
    return body('exchanges')
      .isArray({ min: 1, max: 10 })
      .withMessage('Exchanges must be an array with 1-10 elements')
      .custom((exchanges: string[]) => {
        const validExchanges = ['binance', 'coinbase', 'kraken'];
        for (const exchange of exchanges) {
          if (!validExchanges.includes(exchange.toLowerCase())) {
            throw new Error(`Invalid exchange: ${exchange}`);
          }
        }
        return true;
      })
      .customSanitizer((exchanges: string[]) => 
        exchanges.map(exchange => exchange.toLowerCase().trim())
      );
  }

  /**
   * Validate risk limits configuration
   */
  public static riskLimits(): ValidationChain[] {
    return [
      body('maxPosition')
        .optional()
        .custom((value) => {
          try {
            const amount = new Big(value);
            if (amount.lte(0) || amount.gt(1000000)) {
              throw new Error('Max position must be between 0 and 1,000,000');
            }
            return true;
          } catch (error) {
            throw new Error('Invalid max position format');
          }
        }),
      body('stopLoss')
        .optional()
        .isFloat({ min: 0.001, max: 0.5 })
        .withMessage('Stop loss must be between 0.1% and 50%')
        .toFloat(),
      body('takeProfit')
        .optional()
        .isFloat({ min: 0.001, max: 1.0 })
        .withMessage('Take profit must be between 0.1% and 100%')
        .toFloat()
    ];
  }

  /**
   * Validate API key format (for configuration)
   */
  public static apiKey(): ValidationChain {
    return body('apiKey')
      .isString()
      .isLength({ min: 10, max: 200 })
      .matches(/^[a-zA-Z0-9\-_\.]+$/)
      .withMessage('Invalid API key format')
      .customSanitizer((value: string) => value.trim());
  }

  /**
   * Validate webhook URL
   */
  public static webhookUrl(): ValidationChain {
    return body('webhookUrl')
      .optional()
      .isURL({
        protocols: ['http', 'https'],
        require_protocol: true
      })
      .withMessage('Invalid webhook URL format')
      .isLength({ max: 500 })
      .withMessage('Webhook URL too long');
  }

  /**
   * Sanitize string input to prevent XSS
   */
  public static sanitizeString(field: string, maxLength: number = 255): ValidationChain {
    return body(field)
      .isString()
      .trim()
      .escape()
      .isLength({ max: maxLength })
      .withMessage(`${field} must be less than ${maxLength} characters`);
  }

  /**
   * Validate email format
   */
  public static email(): ValidationChain {
    return body('email')
      .isEmail()
      .withMessage('Invalid email format')
      .normalizeEmail()
      .isLength({ max: 255 })
      .withMessage('Email too long');
  }

  /**
   * Validate password strength
   */
  public static password(): ValidationChain {
    return body('password')
      .isLength({ min: 8, max: 128 })
      .withMessage('Password must be 8-128 characters long')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage('Password must contain uppercase, lowercase, number, and special character');
  }

  /**
   * Validate IP address
   */
  public static ipAddress(): ValidationChain {
    return body('ipAddress')
      .isIP()
      .withMessage('Invalid IP address format');
  }

  /**
   * Custom validator for trading configuration
   */
  public static tradingConfig(): ValidationChain[] {
    return [
      body('config.spreadMethod')
        .isIn(['fixed', 'dynamic', 'volatility'])
        .withMessage('Invalid spread calculation method'),
      body('config.inventoryTarget')
        .isFloat({ min: -1, max: 1 })
        .withMessage('Inventory target must be between -1 and 1')
        .toFloat(),
      body('config.maxOrders')
        .isInt({ min: 1, max: 100 })
        .withMessage('Max orders must be between 1 and 100')
        .toInt(),
      body('config.refreshInterval')
        .isInt({ min: 1000, max: 300000 })
        .withMessage('Refresh interval must be between 1 and 300 seconds')
        .toInt()
    ];
  }

  /**
   * Validate time interval
   */
  public static timeInterval(): ValidationChain {
    return body('interval')
      .isIn(['1m', '5m', '15m', '30m', '1h', '4h', '1d'])
      .withMessage('Invalid time interval');
  }

  /**
   * Rate limiting validation
   */
  public static rateLimitConfig(): ValidationChain[] {
    return [
      body('windowMs')
        .isInt({ min: 1000, max: 3600000 })
        .withMessage('Window must be between 1 second and 1 hour')
        .toInt(),
      body('maxRequests')
        .isInt({ min: 1, max: 10000 })
        .withMessage('Max requests must be between 1 and 10000')
        .toInt()
    ];
  }
}

/**
 * Utility functions for additional validation
 */
export class ValidationUtils {
  
  /**
   * Check if value is a valid decimal number
   */
  public static isValidDecimal(value: any, maxDecimals: number = 8): boolean {
    try {
      const num = new Big(value);
      const decimals = num.toString().split('.')[1]?.length || 0;
      return decimals <= maxDecimals;
    } catch {
      return false;
    }
  }

  /**
   * Sanitize object keys to prevent prototype pollution
   */
  public static sanitizeObject(obj: any): any {
    if (typeof obj !== 'object' || obj === null) {
      return obj;
    }

    const sanitized: any = {};
    for (const key in obj) {
      if (key === '__proto__' || key === 'constructor' || key === 'prototype') {
        continue;
      }
      sanitized[key] = typeof obj[key] === 'object' ? 
        this.sanitizeObject(obj[key]) : obj[key];
    }
    return sanitized;
  }

  /**
   * Validate trading symbol exists in supported pairs
   */
  public static isValidTradingPair(symbol: string): boolean {
    const supportedPairs = [
      'BTC/USD', 'ETH/USD', 'ADA/USD', 'DOT/USD', 'LINK/USD',
      'BTC/USDT', 'ETH/USDT', 'ADA/USDT', 'DOT/USDT', 'LINK/USDT'
    ];
    return supportedPairs.includes(symbol.toUpperCase());
  }

  /**
   * Check if amount meets minimum order size
   */
  public static meetsMinimumSize(amount: string, symbol: string): boolean {
    const minimums: Record<string, string> = {
      'BTC/USD': '0.001',
      'ETH/USD': '0.01',
      'ADA/USD': '1',
      'DOT/USD': '0.1',
      'LINK/USD': '0.1'
    };

    try {
      const amt = new Big(amount);
      const min = new Big(minimums[symbol] || '0.001');
      return amt.gte(min);
    } catch {
      return false;
    }
  }
}
