/**
 * Global Styles for AlphaMarketMaker Dashboard
 * 
 * Tailwind CSS imports, custom utilities, and global styles
 * optimized for dark theme trading interface.
 * 
 * Git Commit: "feat: add global CSS with Tailwind and custom trading styles"
 */

@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom CSS Variables */
:root {
  --color-profit: #22c55e;
  --color-loss: #ef4444;
  --color-neutral: #64748b;
  --color-warning: #f59e0b;
  --color-info: #3b82f6;
  
  --shadow-soft: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  --shadow-medium: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-large: 0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 4px 25px -5px rgba(0, 0, 0, 0.1);
  
  --border-radius: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;
  
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.3s ease-out;
  --transition-slow: 0.5s ease-out;
}

/* Base Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  background-color: #0f172a;
  color: #f1f5f9;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 4px;
  transition: background-color var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

::-webkit-scrollbar-corner {
  background: #1e293b;
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: #475569 #1e293b;
}

/* Focus Styles */
*:focus {
  outline: none;
}

*:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Selection Styles */
::selection {
  background-color: #3b82f6;
  color: #ffffff;
}

::-moz-selection {
  background-color: #3b82f6;
  color: #ffffff;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.25;
  margin: 0;
}

p {
  margin: 0;
}

/* Links */
a {
  color: #3b82f6;
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: #60a5fa;
}

/* Buttons */
button {
  font-family: inherit;
  cursor: pointer;
  transition: all var(--transition-fast);
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Form Elements */
input, textarea, select {
  font-family: inherit;
  transition: all var(--transition-fast);
}

input:disabled, textarea:disabled, select:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Custom Utilities */
@layer utilities {
  /* Text utilities */
  .text-profit {
    color: var(--color-profit);
  }
  
  .text-loss {
    color: var(--color-loss);
  }
  
  .text-neutral {
    color: var(--color-neutral);
  }
  
  /* Background utilities */
  .bg-profit {
    background-color: var(--color-profit);
  }
  
  .bg-loss {
    background-color: var(--color-loss);
  }
  
  .bg-card {
    background-color: #1e293b;
    border: 1px solid #334155;
  }
  
  .bg-card-hover {
    background-color: #334155;
  }
  
  /* Border utilities */
  .border-profit {
    border-color: var(--color-profit);
  }
  
  .border-loss {
    border-color: var(--color-loss);
  }
  
  /* Shadow utilities */
  .shadow-soft {
    box-shadow: var(--shadow-soft);
  }
  
  .shadow-medium {
    box-shadow: var(--shadow-medium);
  }
  
  .shadow-large {
    box-shadow: var(--shadow-large);
  }
  
  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }
  
  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  /* Layout utilities */
  .container-dashboard {
    max-width: 1920px;
    margin: 0 auto;
    padding: 0 1rem;
  }
  
  .grid-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
  }
  
  /* Trading specific utilities */
  .price-up {
    color: var(--color-profit);
    position: relative;
  }
  
  .price-down {
    color: var(--color-loss);
    position: relative;
  }
  
  .price-up::after,
  .price-down::after {
    content: '';
    position: absolute;
    right: -16px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
  }
  
  .price-up::after {
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 6px solid var(--color-profit);
  }
  
  .price-down::after {
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 6px solid var(--color-loss);
  }
  
  /* Chart utilities */
  .chart-tooltip {
    background-color: #1e293b !important;
    border: 1px solid #334155 !important;
    border-radius: var(--border-radius) !important;
    color: #f1f5f9 !important;
    box-shadow: var(--shadow-medium) !important;
  }
  
  /* Loading utilities */
  .loading-skeleton {
    background: linear-gradient(90deg, #1e293b 25%, #334155 50%, #1e293b 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }
  
  /* Responsive utilities */
  .hide-mobile {
    @apply hidden md:block;
  }
  
  .show-mobile {
    @apply block md:hidden;
  }
  
  /* Print utilities */
  @media print {
    .no-print {
      display: none !important;
    }
  }
}

/* Custom Components */
@layer components {
  /* Card component */
  .card {
    @apply bg-card rounded-lg p-6 shadow-soft;
  }
  
  .card-hover {
    @apply card transition-all duration-200 hover:bg-card-hover hover:shadow-medium;
  }
  
  /* Button components */
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-dark-900;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-dark-700 text-dark-200 hover:bg-dark-600 focus:ring-dark-500;
  }
  
  .btn-success {
    @apply btn bg-profit-600 text-white hover:bg-profit-700 focus:ring-profit-500;
  }
  
  .btn-danger {
    @apply btn bg-loss-600 text-white hover:bg-loss-700 focus:ring-loss-500;
  }
  
  /* Input components */
  .input {
    @apply w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-lg text-dark-100 placeholder-dark-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }
  
  /* Badge components */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-success {
    @apply badge bg-profit-100 text-profit-800;
  }
  
  .badge-danger {
    @apply badge bg-loss-100 text-loss-800;
  }
  
  .badge-warning {
    @apply badge bg-yellow-100 text-yellow-800;
  }
  
  .badge-info {
    @apply badge bg-blue-100 text-blue-800;
  }
}

/* Keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .container-dashboard {
    padding: 0 0.75rem;
  }
  
  .grid-dashboard {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .card {
    @apply p-4;
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  body {
    -webkit-font-smoothing: subpixel-antialiased;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
