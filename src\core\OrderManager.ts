/**
 * Order Management System for AlphaMarketMaker
 * 
 * Comprehensive order management with placement, tracking, cancellation,
 * and execution across multiple exchanges with intelligent routing.
 * 
 * Git Commit: "feat: add OrderManager for multi-exchange order execution"
 */

import { EventEmitter } from 'events';
import Big from 'big.js';
import { Config } from '@/utils/Config';
import { Logger } from '@/utils/Logger';
import { DatabaseManager, OrderRecord } from '@/data/DatabaseManager';
import { BaseExchange, Order, OrderRequest } from '@/exchanges/BaseExchange';

export interface ManagedOrder extends Order {
  exchange: string;
  strategy: string;
  parentOrderId?: string;
  childOrderIds: string[];
  metadata: {
    createdAt: number;
    updatedAt: number;
    retryCount: number;
    lastError?: string;
    executionTime?: number;
  };
}

export interface OrderExecutionRequest extends OrderRequest {
  exchange?: string; // Specific exchange or auto-route
  strategy?: string; // Trading strategy identifier
  parentOrderId?: string; // For child orders
  maxRetries?: number;
  timeout?: number;
}

export interface OrderExecutionResult {
  success: boolean;
  order?: ManagedOrder;
  error?: string;
  executionTime: number;
  exchange: string;
}

export interface OrderRouting {
  symbol: string;
  side: 'buy' | 'sell';
  amount: string;
  exchanges: Array<{
    name: string;
    allocation: number; // Percentage allocation
    expectedPrice: string;
    liquidity: string;
    fees: string;
  }>;
  bestExchange: string;
  estimatedSlippage: string;
}

export interface OrderStatistics {
  symbol: string;
  totalOrders: number;
  filledOrders: number;
  canceledOrders: number;
  rejectedOrders: number;
  fillRate: number;
  avgExecutionTime: number;
  avgSlippage: string;
  totalVolume: string;
  totalFees: string;
  lastUpdate: number;
}

export class OrderManager extends EventEmitter {
  private config: Config;
  private logger: Logger;
  private dbManager: DatabaseManager;
  private exchanges: Map<string, BaseExchange> = new Map();

  private activeOrders: Map<string, ManagedOrder> = new Map();
  private orderHistory: Map<string, ManagedOrder[]> = new Map();
  private orderStatistics: Map<string, OrderStatistics> = new Map();
  private retryQueues: Map<string, OrderExecutionRequest[]> = new Map();

  // Order management configuration
  private readonly DEFAULT_TIMEOUT = 30000; // 30 seconds
  private readonly MAX_RETRIES = 3;
  private readonly RETRY_DELAY = 1000; // 1 second
  private readonly CLEANUP_INTERVAL = 300000; // 5 minutes

  constructor(config: Config, logger: Logger, dbManager: DatabaseManager) {
    super();
    this.config = config;
    this.logger = logger.child({ component: 'order-manager' });
    this.dbManager = dbManager;

    this.startCleanupInterval();
  }

  /**
   * Register an exchange for order execution
   */
  public registerExchange(name: string, exchange: BaseExchange): void {
    this.exchanges.set(name, exchange);
    
    // Set up order event listeners
    exchange.on('order', (order: Order) => {
      this.handleOrderUpdate(name, order);
    });

    this.logger.info(`Exchange ${name} registered for order management`);
  }

  /**
   * Place an order with intelligent routing
   */
  public async placeOrder(request: OrderExecutionRequest): Promise<ManagedOrder> {
    const startTime = Date.now();
    
    try {
      this.logger.info('Placing order', {
        symbol: request.symbol,
        side: request.side,
        amount: request.amount,
        price: request.price,
        type: request.type
      });

      // Determine best exchange for execution
      const targetExchange = request.exchange || await this.selectBestExchange(request);
      const exchange = this.exchanges.get(targetExchange);

      if (!exchange) {
        throw new Error(`Exchange ${targetExchange} not available`);
      }

      // Execute order on selected exchange
      const order = await exchange.placeOrder(request);
      
      // Create managed order
      const managedOrder: ManagedOrder = {
        ...order,
        exchange: targetExchange,
        strategy: request.strategy || 'market-making',
        parentOrderId: request.parentOrderId,
        childOrderIds: [],
        metadata: {
          createdAt: startTime,
          updatedAt: Date.now(),
          retryCount: 0,
          executionTime: Date.now() - startTime
        }
      };

      // Store in active orders
      this.activeOrders.set(order.id, managedOrder);

      // Update parent order if this is a child order
      if (request.parentOrderId) {
        const parentOrder = this.activeOrders.get(request.parentOrderId);
        if (parentOrder) {
          parentOrder.childOrderIds.push(order.id);
        }
      }

      // Save to database
      await this.saveOrderToDatabase(managedOrder);

      // Update statistics
      await this.updateOrderStatistics(managedOrder.symbol, 'placed');

      this.emit('orderPlaced', managedOrder);
      
      this.logger.order('Order placed successfully', {
        exchange: targetExchange,
        symbol: order.symbol,
        side: order.side,
        amount: order.amount,
        price: order.price || '0',
        orderId: order.id,
        status: order.status
      });

      return managedOrder;

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      this.logger.error('Failed to place order:', error, {
        symbol: request.symbol,
        side: request.side,
        amount: request.amount,
        executionTime
      });

      // Retry logic
      if ((request.maxRetries || this.MAX_RETRIES) > 0) {
        await this.scheduleRetry(request, error as Error);
      }

      throw error;
    }
  }

  /**
   * Cancel an order
   */
  public async cancelOrder(orderId: string, symbol: string): Promise<boolean> {
    try {
      const managedOrder = this.activeOrders.get(orderId);
      if (!managedOrder) {
        this.logger.warn(`Order ${orderId} not found in active orders`);
        return false;
      }

      const exchange = this.exchanges.get(managedOrder.exchange);
      if (!exchange) {
        throw new Error(`Exchange ${managedOrder.exchange} not available`);
      }

      const success = await exchange.cancelOrder({
        orderId,
        symbol,
        clientOrderId: managedOrder.clientOrderId
      });

      if (success) {
        managedOrder.status = 'canceled';
        managedOrder.metadata.updatedAt = Date.now();
        
        // Move to history
        this.moveOrderToHistory(managedOrder);
        
        // Update statistics
        await this.updateOrderStatistics(symbol, 'canceled');

        this.emit('orderCanceled', managedOrder);
        
        this.logger.order('Order canceled successfully', {
          exchange: managedOrder.exchange,
          symbol: managedOrder.symbol,
          side: managedOrder.side,
          amount: managedOrder.amount,
          price: managedOrder.price || '0',
          orderId: managedOrder.id,
          status: managedOrder.status
        });
      }

      return success;

    } catch (error) {
      this.logger.error(`Failed to cancel order ${orderId}:`, error);
      return false;
    }
  }

  /**
   * Cancel all orders for a symbol
   */
  public async cancelAllOrders(symbol?: string): Promise<number> {
    let canceledCount = 0;

    try {
      const ordersToCancel = Array.from(this.activeOrders.values())
        .filter(order => !symbol || order.symbol === symbol)
        .filter(order => order.status === 'open');

      this.logger.info(`Canceling ${ordersToCancel.length} orders`, { symbol });

      // Group orders by exchange for batch cancellation
      const ordersByExchange = new Map<string, ManagedOrder[]>();
      
      for (const order of ordersToCancel) {
        if (!ordersByExchange.has(order.exchange)) {
          ordersByExchange.set(order.exchange, []);
        }
        ordersByExchange.get(order.exchange)!.push(order);
      }

      // Cancel orders on each exchange
      for (const [exchangeName, orders] of ordersByExchange) {
        const exchange = this.exchanges.get(exchangeName);
        if (!exchange) {
          continue;
        }

        try {
          // Try batch cancellation first
          const batchSuccess = await exchange.cancelAllOrders(symbol);
          
          if (batchSuccess) {
            // Mark all orders as canceled
            for (const order of orders) {
              order.status = 'canceled';
              order.metadata.updatedAt = Date.now();
              this.moveOrderToHistory(order);
              canceledCount++;
            }
          } else {
            // Fall back to individual cancellation
            for (const order of orders) {
              const success = await this.cancelOrder(order.id, order.symbol);
              if (success) {
                canceledCount++;
              }
            }
          }
        } catch (error) {
          this.logger.error(`Failed to cancel orders on ${exchangeName}:`, error);
        }
      }

      this.logger.info(`Canceled ${canceledCount} orders`, { symbol });
      return canceledCount;

    } catch (error) {
      this.logger.error('Failed to cancel all orders:', error);
      return canceledCount;
    }
  }

  /**
   * Get order status
   */
  public async getOrder(orderId: string): Promise<ManagedOrder | null> {
    // Check active orders first
    const activeOrder = this.activeOrders.get(orderId);
    if (activeOrder) {
      return activeOrder;
    }

    // Check history
    for (const orders of this.orderHistory.values()) {
      const historicalOrder = orders.find(order => order.id === orderId);
      if (historicalOrder) {
        return historicalOrder;
      }
    }

    return null;
  }

  /**
   * Get all active orders
   */
  public getActiveOrders(symbol?: string): ManagedOrder[] {
    const orders = Array.from(this.activeOrders.values());
    return symbol ? orders.filter(order => order.symbol === symbol) : orders;
  }

  /**
   * Get order history
   */
  public getOrderHistory(symbol?: string, limit: number = 100): ManagedOrder[] {
    if (symbol) {
      const symbolHistory = this.orderHistory.get(symbol) || [];
      return symbolHistory.slice(-limit);
    }

    // Get all history
    const allHistory: ManagedOrder[] = [];
    for (const orders of this.orderHistory.values()) {
      allHistory.push(...orders);
    }

    // Sort by timestamp and limit
    return allHistory
      .sort((a, b) => b.metadata.createdAt - a.metadata.createdAt)
      .slice(0, limit);
  }

  /**
   * Select best exchange for order execution
   */
  private async selectBestExchange(request: OrderExecutionRequest): Promise<string> {
    try {
      const routing = await this.calculateOrderRouting(request);
      return routing.bestExchange;
    } catch (error) {
      this.logger.error('Failed to calculate order routing:', error);
      
      // Fall back to first available exchange
      const availableExchanges = Array.from(this.exchanges.keys());
      if (availableExchanges.length === 0) {
        throw new Error('No exchanges available');
      }
      
      return availableExchanges[0];
    }
  }

  /**
   * Calculate optimal order routing
   */
  private async calculateOrderRouting(request: OrderExecutionRequest): Promise<OrderRouting> {
    const exchanges: OrderRouting['exchanges'] = [];
    let bestExchange = '';
    let bestScore = -1;

    for (const [name, exchange] of this.exchanges) {
      try {
        // Get market data for routing decision
        const ticker = await exchange.getTicker(request.symbol);
        const orderBook = await exchange.getOrderBook(request.symbol, 10);
        const fees = await exchange.getTradingFees(request.symbol);

        // Calculate liquidity and expected price
        const relevantSide = request.side === 'buy' ? orderBook.asks : orderBook.bids;
        const liquidity = relevantSide.slice(0, 5)
          .reduce((sum, [_, size]) => sum + parseFloat(size), 0);

        const expectedPrice = request.side === 'buy' ? ticker.ask : ticker.bid;
        const feeRate = parseFloat(request.type === 'market' ? fees.taker : fees.maker);

        // Calculate routing score (higher is better)
        let score = 0;
        score += liquidity * 0.4; // 40% weight for liquidity
        score += (1 - feeRate) * 0.3; // 30% weight for low fees
        score += exchange.isExchangeConnected() ? 0.3 : 0; // 30% weight for connectivity

        exchanges.push({
          name,
          allocation: 100, // Single exchange for now
          expectedPrice,
          liquidity: liquidity.toString(),
          fees: feeRate.toString()
        });

        if (score > bestScore) {
          bestScore = score;
          bestExchange = name;
        }

      } catch (error) {
        this.logger.error(`Failed to evaluate exchange ${name} for routing:`, error);
      }
    }

    if (!bestExchange) {
      throw new Error('No suitable exchange found for order routing');
    }

    return {
      symbol: request.symbol,
      side: request.side,
      amount: request.amount,
      exchanges,
      bestExchange,
      estimatedSlippage: '0.001' // Placeholder
    };
  }

  /**
   * Handle order update from exchange
   */
  private async handleOrderUpdate(exchangeName: string, order: Order): Promise<void> {
    const managedOrder = this.activeOrders.get(order.id);
    if (!managedOrder) {
      return;
    }

    // Update managed order
    Object.assign(managedOrder, order);
    managedOrder.metadata.updatedAt = Date.now();

    // Handle status changes
    if (order.status === 'closed' || order.status === 'canceled' || order.status === 'rejected') {
      this.moveOrderToHistory(managedOrder);
      
      // Update statistics
      await this.updateOrderStatistics(order.symbol, order.status);
    }

    // Save to database
    await this.updateOrderInDatabase(managedOrder);

    this.emit('orderUpdated', managedOrder);
    
    if (order.status === 'closed') {
      this.emit('orderFilled', managedOrder);
    }
  }

  /**
   * Move order from active to history
   */
  private moveOrderToHistory(order: ManagedOrder): void {
    this.activeOrders.delete(order.id);
    
    if (!this.orderHistory.has(order.symbol)) {
      this.orderHistory.set(order.symbol, []);
    }
    
    const symbolHistory = this.orderHistory.get(order.symbol)!;
    symbolHistory.push(order);
    
    // Keep only recent history (last 1000 orders per symbol)
    if (symbolHistory.length > 1000) {
      symbolHistory.splice(0, symbolHistory.length - 1000);
    }
  }

  /**
   * Save order to database
   */
  private async saveOrderToDatabase(order: ManagedOrder): Promise<void> {
    try {
      const orderRecord: OrderRecord = {
        exchange: order.exchange,
        symbol: order.symbol,
        side: order.side,
        type: order.type,
        amount: order.amount,
        price: order.price || '0',
        filled: order.filled,
        remaining: order.remaining,
        status: order.status,
        orderId: order.id,
        clientOrderId: order.clientOrderId,
        timestamp: new Date(order.timestamp)
      };

      await this.dbManager.insertOrder(orderRecord);
    } catch (error) {
      this.logger.error('Failed to save order to database:', error);
    }
  }

  /**
   * Update order in database
   */
  private async updateOrderInDatabase(order: ManagedOrder): Promise<void> {
    try {
      const updates = {
        filled: order.filled,
        remaining: order.remaining,
        status: order.status
      };

      await this.dbManager.updateOrder(order.id, updates);
    } catch (error) {
      this.logger.error('Failed to update order in database:', error);
    }
  }

  /**
   * Update order statistics
   */
  private async updateOrderStatistics(symbol: string, event: string): Promise<void> {
    try {
      let stats = this.orderStatistics.get(symbol);
      
      if (!stats) {
        stats = {
          symbol,
          totalOrders: 0,
          filledOrders: 0,
          canceledOrders: 0,
          rejectedOrders: 0,
          fillRate: 0,
          avgExecutionTime: 0,
          avgSlippage: '0',
          totalVolume: '0',
          totalFees: '0',
          lastUpdate: Date.now()
        };
        this.orderStatistics.set(symbol, stats);
      }

      // Update counters
      switch (event) {
        case 'placed':
          stats.totalOrders++;
          break;
        case 'closed':
          stats.filledOrders++;
          break;
        case 'canceled':
          stats.canceledOrders++;
          break;
        case 'rejected':
          stats.rejectedOrders++;
          break;
      }

      // Recalculate fill rate
      stats.fillRate = stats.totalOrders > 0 ? stats.filledOrders / stats.totalOrders : 0;
      stats.lastUpdate = Date.now();

      this.emit('statisticsUpdated', symbol, stats);

    } catch (error) {
      this.logger.error(`Failed to update statistics for ${symbol}:`, error);
    }
  }

  /**
   * Schedule order retry
   */
  private async scheduleRetry(request: OrderExecutionRequest, error: Error): Promise<void> {
    const retryCount = (request.maxRetries || this.MAX_RETRIES) - 1;
    
    if (retryCount <= 0) {
      this.logger.error('Max retries reached for order', {
        symbol: request.symbol,
        error: error.message
      });
      return;
    }

    const retryRequest = {
      ...request,
      maxRetries: retryCount
    };

    // Add to retry queue
    const queueKey = `${request.symbol}_${request.side}`;
    if (!this.retryQueues.has(queueKey)) {
      this.retryQueues.set(queueKey, []);
    }
    
    this.retryQueues.get(queueKey)!.push(retryRequest);

    // Schedule retry
    setTimeout(async () => {
      try {
        await this.placeOrder(retryRequest);
      } catch (retryError) {
        this.logger.error('Order retry failed:', retryError);
      }
    }, this.RETRY_DELAY * (this.MAX_RETRIES - retryCount));

    this.logger.info(`Order retry scheduled`, {
      symbol: request.symbol,
      retriesLeft: retryCount,
      delay: this.RETRY_DELAY * (this.MAX_RETRIES - retryCount)
    });
  }

  /**
   * Get order statistics
   */
  public getOrderStatistics(symbol?: string): OrderStatistics[] {
    if (symbol) {
      const stats = this.orderStatistics.get(symbol);
      return stats ? [stats] : [];
    }
    
    return Array.from(this.orderStatistics.values());
  }

  /**
   * Start cleanup interval for old orders
   */
  private startCleanupInterval(): void {
    setInterval(() => {
      this.cleanupOldOrders();
    }, this.CLEANUP_INTERVAL);
  }

  /**
   * Clean up old orders from memory
   */
  private cleanupOldOrders(): void {
    const cutoffTime = Date.now() - (24 * 60 * 60 * 1000); // 24 hours ago
    
    for (const [symbol, orders] of this.orderHistory) {
      const filteredOrders = orders.filter(order => 
        order.metadata.createdAt > cutoffTime
      );
      
      if (filteredOrders.length !== orders.length) {
        this.orderHistory.set(symbol, filteredOrders);
        this.logger.debug(`Cleaned up old orders for ${symbol}`, {
          removed: orders.length - filteredOrders.length,
          remaining: filteredOrders.length
        });
      }
    }
  }

  /**
   * Get system health status
   */
  public getHealthStatus(): {
    status: 'healthy' | 'degraded' | 'unhealthy';
    activeOrders: number;
    exchanges: number;
    avgExecutionTime: number;
  } {
    const activeOrderCount = this.activeOrders.size;
    const exchangeCount = this.exchanges.size;
    
    // Calculate average execution time
    const recentOrders = Array.from(this.activeOrders.values())
      .filter(order => order.metadata.executionTime)
      .slice(-100);
    
    const avgExecutionTime = recentOrders.length > 0
      ? recentOrders.reduce((sum, order) => sum + (order.metadata.executionTime || 0), 0) / recentOrders.length
      : 0;

    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    
    if (exchangeCount === 0) {
      status = 'unhealthy';
    } else if (avgExecutionTime > 10000 || activeOrderCount > 1000) {
      status = 'degraded';
    }

    return {
      status,
      activeOrders: activeOrderCount,
      exchanges: exchangeCount,
      avgExecutionTime
    };
  }
}
