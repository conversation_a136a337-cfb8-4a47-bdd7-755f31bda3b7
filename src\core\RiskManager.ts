/**
 * Risk Management System for AlphaMarketMaker
 * 
 * Comprehensive risk management with inventory limits, position sizing,
 * stop-loss mechanisms, and real-time risk monitoring.
 * 
 * Git Commit: "feat: add RiskManager with inventory limits and position controls"
 */

import { EventEmitter } from 'events';
import Big from 'big.js';
import { Config, RiskLimits } from '@/utils/Config';
import { Logger } from '@/utils/Logger';
import { DatabaseManager } from '@/data/DatabaseManager';

export interface RiskCheck {
  allowed: boolean;
  reason?: string;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  limits: {
    position: { current: string; max: string; utilization: number };
    dailyLoss: { current: string; max: string; utilization: number };
    inventory: { current: string; target: string; deviation: number };
  };
}

export interface OrderRiskRequest {
  symbol: string;
  side: 'buy' | 'sell';
  amount: string;
  price: string;
  exchange?: string;
}

export interface RiskAlert {
  id: string;
  symbol: string;
  type: 'position_limit' | 'daily_loss' | 'inventory_deviation' | 'volatility' | 'liquidity';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  currentValue: string;
  threshold: string;
  timestamp: number;
  acknowledged: boolean;
}

export interface PositionRisk {
  symbol: string;
  currentPosition: string;
  maxPosition: string;
  utilizationPercent: number;
  inventoryDeviation: number;
  unrealizedPnl: string;
  riskScore: number; // 0-100
  lastUpdate: number;
}

export interface DailyRiskMetrics {
  date: string;
  totalPnl: string;
  realizedPnl: string;
  unrealizedPnl: string;
  maxDrawdown: string;
  sharpeRatio: number;
  winRate: number;
  totalTrades: number;
  avgTradeSize: string;
  maxPosition: string;
}

export class RiskManager extends EventEmitter {
  private config: Config;
  private logger: Logger;
  private dbManager: DatabaseManager;
  private riskLimits: RiskLimits;

  private positions: Map<string, PositionRisk> = new Map();
  private dailyMetrics: DailyRiskMetrics | null = null;
  private activeAlerts: Map<string, RiskAlert> = new Map();
  private riskCheckInterval: NodeJS.Timeout | null = null;

  // Risk calculation parameters
  private readonly RISK_CHECK_INTERVAL = 5000; // 5 seconds
  private readonly VOLATILITY_WINDOW = 100; // Price points for volatility
  private readonly MAX_RISK_SCORE = 100;
  private readonly CRITICAL_RISK_THRESHOLD = 80;

  constructor(config: Config, logger: Logger, dbManager: DatabaseManager) {
    super();
    this.config = config;
    this.logger = logger.child({ component: 'risk-manager' });
    this.dbManager = dbManager;
    this.riskLimits = config.getRiskLimits();

    this.startRiskMonitoring();
  }

  /**
   * Check if an order meets risk requirements
   */
  public async checkOrderRisk(symbol: string, order: OrderRiskRequest): Promise<RiskCheck> {
    try {
      const currentPosition = await this.getCurrentPosition(symbol);
      const dailyPnl = await this.getDailyPnl();
      
      // Calculate new position after order
      const orderAmount = new Big(order.amount);
      const positionChange = order.side === 'buy' ? orderAmount : orderAmount.mul(-1);
      const newPosition = currentPosition.plus(positionChange);

      // Check position limits
      const maxPosition = new Big(this.riskLimits.maxPositionSize);
      const positionUtilization = newPosition.abs().div(maxPosition).toNumber();

      if (newPosition.abs().gt(maxPosition)) {
        return {
          allowed: false,
          reason: `Position limit exceeded: ${newPosition.toFixed(8)} > ${maxPosition.toFixed(8)}`,
          riskLevel: 'critical',
          limits: {
            position: {
              current: newPosition.toFixed(8),
              max: maxPosition.toFixed(8),
              utilization: positionUtilization
            },
            dailyLoss: {
              current: dailyPnl.toFixed(2),
              max: this.riskLimits.maxDailyLoss.toString(),
              utilization: Math.abs(dailyPnl.toNumber()) / this.riskLimits.maxDailyLoss
            },
            inventory: {
              current: currentPosition.toFixed(8),
              target: '0',
              deviation: Math.abs(currentPosition.toNumber())
            }
          }
        };
      }

      // Check daily loss limits
      const maxDailyLoss = new Big(this.riskLimits.maxDailyLoss);
      const dailyLossUtilization = dailyPnl.abs().div(maxDailyLoss).toNumber();

      if (dailyPnl.lt(maxDailyLoss.mul(-1))) {
        return {
          allowed: false,
          reason: `Daily loss limit exceeded: ${dailyPnl.toFixed(2)} < -${maxDailyLoss.toFixed(2)}`,
          riskLevel: 'critical',
          limits: {
            position: {
              current: currentPosition.toFixed(8),
              max: maxPosition.toFixed(8),
              utilization: Math.abs(currentPosition.toNumber()) / maxPosition.toNumber()
            },
            dailyLoss: {
              current: dailyPnl.toFixed(2),
              max: maxDailyLoss.toString(),
              utilization: dailyLossUtilization
            },
            inventory: {
              current: currentPosition.toFixed(8),
              target: '0',
              deviation: Math.abs(currentPosition.toNumber())
            }
          }
        };
      }

      // Check inventory deviation
      const inventoryLimit = new Big(this.riskLimits.inventoryLimit);
      const inventoryDeviation = Math.abs(currentPosition.toNumber());

      // Determine risk level
      let riskLevel: 'low' | 'medium' | 'high' | 'critical' = 'low';
      
      if (positionUtilization > 0.8 || dailyLossUtilization > 0.8) {
        riskLevel = 'critical';
      } else if (positionUtilization > 0.6 || dailyLossUtilization > 0.6) {
        riskLevel = 'high';
      } else if (positionUtilization > 0.4 || dailyLossUtilization > 0.4) {
        riskLevel = 'medium';
      }

      return {
        allowed: true,
        riskLevel,
        limits: {
          position: {
            current: newPosition.toFixed(8),
            max: maxPosition.toFixed(8),
            utilization: positionUtilization
          },
          dailyLoss: {
            current: dailyPnl.toFixed(2),
            max: maxDailyLoss.toString(),
            utilization: dailyLossUtilization
          },
          inventory: {
            current: currentPosition.toFixed(8),
            target: '0',
            deviation: inventoryDeviation
          }
        }
      };

    } catch (error) {
      this.logger.error(`Risk check failed for ${symbol}:`, error);
      return {
        allowed: false,
        reason: 'Risk check system error',
        riskLevel: 'critical',
        limits: {
          position: { current: '0', max: '0', utilization: 0 },
          dailyLoss: { current: '0', max: '0', utilization: 0 },
          inventory: { current: '0', target: '0', deviation: 0 }
        }
      };
    }
  }

  /**
   * Get current position for a symbol
   */
  private async getCurrentPosition(symbol: string): Promise<Big> {
    try {
      const positions = await this.dbManager.getPositions();
      const position = positions.find(p => p.symbol === symbol);
      return new Big(position?.size || '0');
    } catch (error) {
      this.logger.error(`Failed to get position for ${symbol}:`, error);
      return new Big('0');
    }
  }

  /**
   * Get daily P&L
   */
  private async getDailyPnl(): Promise<Big> {
    try {
      const today = new Date().toISOString().split('T')[0];
      const startOfDay = new Date(`${today}T00:00:00Z`);
      
      const { trades } = await this.dbManager.getTrades({
        startDate: startOfDay,
        endDate: new Date()
      });

      let totalPnl = new Big('0');
      
      for (const trade of trades) {
        const amount = new Big(trade.amount);
        const price = new Big(trade.price);
        const fee = new Big(trade.fee);
        
        const tradeValue = amount.mul(price);
        const tradePnl = trade.side === 'sell' ? tradeValue.minus(fee) : tradeValue.plus(fee).mul(-1);
        
        totalPnl = totalPnl.plus(tradePnl);
      }

      return totalPnl;
    } catch (error) {
      this.logger.error('Failed to calculate daily P&L:', error);
      return new Big('0');
    }
  }

  /**
   * Update position risk metrics
   */
  public async updatePositionRisk(symbol: string): Promise<void> {
    try {
      const currentPosition = await this.getCurrentPosition(symbol);
      const maxPosition = new Big(this.riskLimits.maxPositionSize);
      const utilizationPercent = currentPosition.abs().div(maxPosition).mul(100).toNumber();
      
      // Get unrealized P&L (would need market price)
      const unrealizedPnl = '0'; // Placeholder - would calculate based on current market price
      
      // Calculate inventory deviation from target (assuming target is 0)
      const inventoryDeviation = Math.abs(currentPosition.toNumber());
      
      // Calculate risk score (0-100)
      let riskScore = 0;
      riskScore += utilizationPercent * 0.4; // 40% weight for position utilization
      riskScore += Math.min(inventoryDeviation * 100, 30); // 30% max for inventory deviation
      riskScore += Math.min(Math.abs(parseFloat(unrealizedPnl)) / 1000 * 30, 30); // 30% max for unrealized P&L
      
      const positionRisk: PositionRisk = {
        symbol,
        currentPosition: currentPosition.toFixed(8),
        maxPosition: maxPosition.toFixed(8),
        utilizationPercent,
        inventoryDeviation,
        unrealizedPnl,
        riskScore: Math.min(riskScore, this.MAX_RISK_SCORE),
        lastUpdate: Date.now()
      };

      this.positions.set(symbol, positionRisk);

      // Check for risk alerts
      await this.checkRiskAlerts(symbol, positionRisk);

      this.emit('positionRiskUpdated', symbol, positionRisk);

    } catch (error) {
      this.logger.error(`Failed to update position risk for ${symbol}:`, error);
    }
  }

  /**
   * Check for risk alerts
   */
  private async checkRiskAlerts(symbol: string, positionRisk: PositionRisk): Promise<void> {
    const alerts: RiskAlert[] = [];

    // Position limit alert
    if (positionRisk.utilizationPercent > 80) {
      alerts.push({
        id: `position_${symbol}_${Date.now()}`,
        symbol,
        type: 'position_limit',
        severity: positionRisk.utilizationPercent > 95 ? 'critical' : 'high',
        message: `Position utilization at ${positionRisk.utilizationPercent.toFixed(1)}%`,
        currentValue: positionRisk.currentPosition,
        threshold: positionRisk.maxPosition,
        timestamp: Date.now(),
        acknowledged: false
      });
    }

    // Inventory deviation alert
    if (positionRisk.inventoryDeviation > this.riskLimits.inventoryLimit) {
      alerts.push({
        id: `inventory_${symbol}_${Date.now()}`,
        symbol,
        type: 'inventory_deviation',
        severity: positionRisk.inventoryDeviation > this.riskLimits.inventoryLimit * 1.5 ? 'high' : 'medium',
        message: `Inventory deviation: ${positionRisk.inventoryDeviation.toFixed(8)}`,
        currentValue: positionRisk.currentPosition,
        threshold: this.riskLimits.inventoryLimit.toString(),
        timestamp: Date.now(),
        acknowledged: false
      });
    }

    // High risk score alert
    if (positionRisk.riskScore > this.CRITICAL_RISK_THRESHOLD) {
      alerts.push({
        id: `risk_score_${symbol}_${Date.now()}`,
        symbol,
        type: 'volatility',
        severity: positionRisk.riskScore > 90 ? 'critical' : 'high',
        message: `High risk score: ${positionRisk.riskScore.toFixed(1)}`,
        currentValue: positionRisk.riskScore.toString(),
        threshold: this.CRITICAL_RISK_THRESHOLD.toString(),
        timestamp: Date.now(),
        acknowledged: false
      });
    }

    // Process alerts
    for (const alert of alerts) {
      this.activeAlerts.set(alert.id, alert);
      this.emit('riskAlert', alert);
      
      this.logger.risk(`Risk alert: ${alert.message}`, {
        symbol: alert.symbol,
        severity: alert.severity,
        type: alert.type,
        currentValue: alert.currentValue,
        threshold: alert.threshold
      });
    }
  }

  /**
   * Start continuous risk monitoring
   */
  private startRiskMonitoring(): void {
    this.riskCheckInterval = setInterval(async () => {
      try {
        await this.performRiskCheck();
      } catch (error) {
        this.logger.error('Risk monitoring check failed:', error);
      }
    }, this.RISK_CHECK_INTERVAL);

    this.logger.info('Risk monitoring started');
  }

  /**
   * Perform comprehensive risk check
   */
  private async performRiskCheck(): Promise<void> {
    try {
      // Update daily metrics
      await this.updateDailyMetrics();

      // Check all positions
      const positions = await this.dbManager.getPositions();
      
      for (const position of positions) {
        await this.updatePositionRisk(position.symbol);
      }

      // Check daily loss limits
      const dailyPnl = await this.getDailyPnl();
      const maxDailyLoss = new Big(this.riskLimits.maxDailyLoss);
      
      if (dailyPnl.lt(maxDailyLoss.mul(-1))) {
        const alert: RiskAlert = {
          id: `daily_loss_${Date.now()}`,
          symbol: 'ALL',
          type: 'daily_loss',
          severity: 'critical',
          message: `Daily loss limit exceeded: ${dailyPnl.toFixed(2)}`,
          currentValue: dailyPnl.toFixed(2),
          threshold: maxDailyLoss.mul(-1).toFixed(2),
          timestamp: Date.now(),
          acknowledged: false
        };

        this.activeAlerts.set(alert.id, alert);
        this.emit('riskAlert', alert);
        this.emit('dailyLossLimit');
      }

    } catch (error) {
      this.logger.error('Risk check failed:', error);
    }
  }

  /**
   * Update daily risk metrics
   */
  private async updateDailyMetrics(): Promise<void> {
    try {
      const today = new Date().toISOString().split('T')[0];
      const startOfDay = new Date(`${today}T00:00:00Z`);
      
      const { trades } = await this.dbManager.getTrades({
        startDate: startOfDay,
        endDate: new Date()
      });

      let totalPnl = new Big('0');
      let realizedPnl = new Big('0');
      let totalVolume = new Big('0');
      let winningTrades = 0;

      for (const trade of trades) {
        const amount = new Big(trade.amount);
        const price = new Big(trade.price);
        const fee = new Big(trade.fee);
        
        const tradeValue = amount.mul(price);
        totalVolume = totalVolume.plus(tradeValue);
        
        // Simplified P&L calculation
        const tradePnl = trade.side === 'sell' ? tradeValue.minus(fee) : tradeValue.plus(fee).mul(-1);
        totalPnl = totalPnl.plus(tradePnl);
        realizedPnl = realizedPnl.plus(tradePnl);
        
        if (tradePnl.gt(0)) {
          winningTrades++;
        }
      }

      const winRate = trades.length > 0 ? winningTrades / trades.length : 0;
      const avgTradeSize = trades.length > 0 ? totalVolume.div(trades.length) : new Big('0');

      this.dailyMetrics = {
        date: today,
        totalPnl: totalPnl.toFixed(2),
        realizedPnl: realizedPnl.toFixed(2),
        unrealizedPnl: '0', // Would calculate from current positions
        maxDrawdown: '0', // Would track throughout the day
        sharpeRatio: 0, // Would calculate with returns data
        winRate,
        totalTrades: trades.length,
        avgTradeSize: avgTradeSize.toFixed(8),
        maxPosition: this.riskLimits.maxPositionSize.toString()
      };

      this.emit('dailyMetricsUpdated', this.dailyMetrics);

    } catch (error) {
      this.logger.error('Failed to update daily metrics:', error);
    }
  }

  /**
   * Get position risk for a symbol
   */
  public getPositionRisk(symbol: string): PositionRisk | null {
    return this.positions.get(symbol) || null;
  }

  /**
   * Get all position risks
   */
  public getAllPositionRisks(): PositionRisk[] {
    return Array.from(this.positions.values());
  }

  /**
   * Get daily risk metrics
   */
  public getDailyMetrics(): DailyRiskMetrics | null {
    return this.dailyMetrics;
  }

  /**
   * Get active risk alerts
   */
  public getActiveAlerts(): RiskAlert[] {
    return Array.from(this.activeAlerts.values()).filter(alert => !alert.acknowledged);
  }

  /**
   * Acknowledge a risk alert
   */
  public acknowledgeAlert(alertId: string): boolean {
    const alert = this.activeAlerts.get(alertId);
    if (alert) {
      alert.acknowledged = true;
      this.emit('alertAcknowledged', alert);
      return true;
    }
    return false;
  }

  /**
   * Check if trading should be halted due to risk
   */
  public shouldHaltTrading(): boolean {
    const criticalAlerts = Array.from(this.activeAlerts.values())
      .filter(alert => alert.severity === 'critical' && !alert.acknowledged);
    
    return criticalAlerts.length > 0;
  }

  /**
   * Get overall risk status
   */
  public getRiskStatus(): {
    status: 'healthy' | 'warning' | 'critical';
    score: number;
    alerts: number;
    positions: number;
  } {
    const alerts = this.getActiveAlerts();
    const criticalAlerts = alerts.filter(a => a.severity === 'critical');
    const highAlerts = alerts.filter(a => a.severity === 'high');
    
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    let score = 0;

    if (criticalAlerts.length > 0) {
      status = 'critical';
      score = 90;
    } else if (highAlerts.length > 0) {
      status = 'warning';
      score = 60;
    } else if (alerts.length > 0) {
      status = 'warning';
      score = 30;
    }

    return {
      status,
      score,
      alerts: alerts.length,
      positions: this.positions.size
    };
  }

  /**
   * Stop risk monitoring
   */
  public stop(): void {
    if (this.riskCheckInterval) {
      clearInterval(this.riskCheckInterval);
      this.riskCheckInterval = null;
    }
    
    this.positions.clear();
    this.activeAlerts.clear();
    this.logger.info('Risk monitoring stopped');
  }
}
