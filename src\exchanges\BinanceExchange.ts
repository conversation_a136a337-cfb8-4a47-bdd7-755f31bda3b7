/**
 * Binance Exchange Integration for AlphaMarketMaker
 * 
 * Complete Binance API integration with WebSocket market data feeds,
 * REST API for trading operations, and comprehensive error handling.
 * 
 * Git Commit: "feat: add Binance exchange integration with WebSocket and REST API"
 */

import WebSocket from 'ws';
import axios, { AxiosInstance } from 'axios';
import crypto from 'crypto';
import { 
  BaseExchange, 
  Ticker, 
  OrderBook, 
  Trade, 
  Order, 
  Balance, 
  Position, 
  OrderRequest, 
  CancelOrderRequest, 
  ExchangeInfo,
  MarketDataSubscription
} from './BaseExchange';
import { Logger } from '@/utils/Logger';
import { ExchangeConfig } from '@/utils/Config';

interface BinanceTickerData {
  s: string; // symbol
  c: string; // close price
  b: string; // best bid price
  a: string; // best ask price
  v: string; // volume
  P: string; // price change percent
}

interface BinanceOrderBookData {
  s: string; // symbol
  b: [string, string][]; // bids
  a: [string, string][]; // asks
}

interface BinanceTradeData {
  s: string; // symbol
  t: number; // trade ID
  p: string; // price
  q: string; // quantity
  T: number; // trade time
  m: boolean; // is buyer maker
}

interface BinanceOrderData {
  symbol: string;
  orderId: number;
  clientOrderId: string;
  price: string;
  origQty: string;
  executedQty: string;
  cummulativeQuoteQty: string;
  status: string;
  timeInForce: string;
  type: string;
  side: string;
  fills?: Array<{
    price: string;
    qty: string;
    commission: string;
    commissionAsset: string;
  }>;
  transactTime: number;
}

export class BinanceExchange extends BaseExchange {
  private restClient: AxiosInstance;
  private wsClient: WebSocket | null = null;
  private wsUrl: string;
  private restUrl: string;
  private subscriptions: Set<string> = new Set();
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 10;
  private reconnectDelay: number = 1000;

  constructor(config: ExchangeConfig, logger: Logger) {
    super('binance', config, logger);
    
    this.restUrl = config.sandbox 
      ? 'https://testnet.binance.vision/api'
      : 'https://api.binance.com/api';
    
    this.wsUrl = config.sandbox
      ? 'wss://testnet.binance.vision/ws'
      : 'wss://stream.binance.com:9443/ws';

    this.initializeRestClient();
  }

  /**
   * Initialize REST API client with authentication
   */
  private initializeRestClient(): void {
    this.restClient = axios.create({
      baseURL: this.restUrl,
      timeout: this.config.timeout || 10000,
      headers: {
        'X-MBX-APIKEY': this.config.apiKey,
        'Content-Type': 'application/json'
      }
    });

    // Request interceptor for authentication
    this.restClient.interceptors.request.use((config) => {
      if (config.method === 'post' || config.method === 'put' || config.method === 'delete') {
        const timestamp = Date.now();
        const queryString = new URLSearchParams(config.params || {}).toString();
        const signature = this.createSignature(queryString + `&timestamp=${timestamp}`);
        
        config.params = {
          ...config.params,
          timestamp,
          signature
        };
      }
      return config;
    });

    // Response interceptor for error handling
    this.restClient.interceptors.response.use(
      (response) => response,
      (error) => {
        this.logger.error('Binance REST API error:', {
          status: error.response?.status,
          data: error.response?.data,
          url: error.config?.url
        });
        throw error;
      }
    );
  }

  /**
   * Create HMAC SHA256 signature for authenticated requests
   */
  private createSignature(queryString: string): string {
    return crypto
      .createHmac('sha256', this.config.secretKey)
      .update(queryString)
      .digest('hex');
  }

  /**
   * Initialize exchange connection
   */
  public async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing Binance exchange...');

      // Test REST API connection
      await this.testConnection();

      // Initialize WebSocket connection
      await this.initializeWebSocket();

      this.logger.info('Binance exchange initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Binance exchange:', error);
      throw error;
    }
  }

  /**
   * Initialize WebSocket connection
   */
  private async initializeWebSocket(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.wsClient = new WebSocket(this.wsUrl);

        this.wsClient.on('open', () => {
          this.handleWebSocketOpen();
          this.reconnectAttempts = 0;
          resolve();
        });

        this.wsClient.on('message', (data: WebSocket.Data) => {
          this.handleWebSocketMessage(data);
        });

        this.wsClient.on('close', () => {
          this.handleWebSocketClose();
          this.attemptReconnect();
        });

        this.wsClient.on('error', (error: Error) => {
          this.handleWebSocketError(error);
          reject(error);
        });

        // Set up ping/pong for connection health
        this.wsClient.on('ping', () => {
          this.wsClient?.pong();
        });

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Handle WebSocket message
   */
  private handleWebSocketMessage(data: WebSocket.Data): void {
    try {
      const message = JSON.parse(data.toString());

      if (message.stream) {
        const [symbol, channel] = message.stream.split('@');
        
        switch (channel) {
          case 'ticker':
            this.handleTickerMessage(message.data);
            break;
          case 'depth':
            this.handleOrderBookMessage(message.data);
            break;
          case 'trade':
            this.handleTradeMessage(message.data);
            break;
        }
      }
    } catch (error) {
      this.logger.error('Failed to parse WebSocket message:', error);
    }
  }

  /**
   * Handle ticker message
   */
  private handleTickerMessage(data: BinanceTickerData): void {
    const ticker: Ticker = {
      symbol: this.denormalizeSymbol(data.s),
      bid: data.b,
      ask: data.a,
      last: data.c,
      volume: data.v,
      change: ((parseFloat(data.c) - parseFloat(data.c)) / parseFloat(data.c) * 100).toString(),
      changePercent: data.P,
      timestamp: Date.now()
    };

    this.handleTickerUpdate(ticker);
  }

  /**
   * Handle order book message
   */
  private handleOrderBookMessage(data: BinanceOrderBookData): void {
    const orderBook: OrderBook = {
      symbol: this.denormalizeSymbol(data.s),
      bids: data.b,
      asks: data.a,
      timestamp: Date.now()
    };

    this.handleOrderBookUpdate(orderBook);
  }

  /**
   * Handle trade message
   */
  private handleTradeMessage(data: BinanceTradeData): void {
    const trade: Trade = {
      id: data.t.toString(),
      symbol: this.denormalizeSymbol(data.s),
      side: data.m ? 'sell' : 'buy', // buyer is maker means sell
      amount: data.q,
      price: data.p,
      timestamp: data.T
    };

    this.handleTradeUpdate(trade);
  }

  /**
   * Attempt WebSocket reconnection
   */
  private async attemptReconnect(): Promise<void> {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.logger.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    this.logger.info(`Attempting WebSocket reconnection in ${delay}ms (attempt ${this.reconnectAttempts})`);
    
    setTimeout(async () => {
      try {
        await this.initializeWebSocket();
        
        // Resubscribe to previous subscriptions
        if (this.subscriptions.size > 0) {
          const symbols = Array.from(this.subscriptions);
          await this.subscribeToMarketData([{
            symbol: symbols[0], // Simplified for example
            channels: ['ticker', 'orderbook', 'trades']
          }]);
        }
      } catch (error) {
        this.logger.error('WebSocket reconnection failed:', error);
      }
    }, delay);
  }

  /**
   * Close exchange connections
   */
  public async close(): Promise<void> {
    if (this.wsClient) {
      this.wsClient.close();
      this.wsClient = null;
    }
    this.logger.info('Binance exchange connections closed');
  }

  /**
   * Get exchange information
   */
  public async getExchangeInfo(): Promise<ExchangeInfo> {
    try {
      await this.checkRateLimit('exchangeInfo');
      const response = await this.restClient.get('/v3/exchangeInfo');
      
      return {
        name: 'binance',
        rateLimits: response.data.rateLimits.map((limit: any) => ({
          requests: limit.limit,
          interval: limit.interval
        })),
        symbols: response.data.symbols.map((symbol: any) => ({
          symbol: this.denormalizeSymbol(symbol.symbol),
          baseAsset: symbol.baseAsset,
          quoteAsset: symbol.quoteAsset,
          status: symbol.status,
          minOrderSize: symbol.filters.find((f: any) => f.filterType === 'LOT_SIZE')?.minQty || '0',
          maxOrderSize: symbol.filters.find((f: any) => f.filterType === 'LOT_SIZE')?.maxQty || '0',
          tickSize: symbol.filters.find((f: any) => f.filterType === 'PRICE_FILTER')?.tickSize || '0',
          stepSize: symbol.filters.find((f: any) => f.filterType === 'LOT_SIZE')?.stepSize || '0'
        }))
      };
    } catch (error) {
      this.logger.error('Failed to get exchange info:', error);
      throw error;
    }
  }

  /**
   * Subscribe to market data
   */
  public async subscribeToMarketData(subscriptions: MarketDataSubscription[]): Promise<void> {
    if (!this.wsClient || this.wsClient.readyState !== WebSocket.OPEN) {
      throw new Error('WebSocket not connected');
    }

    const streams: string[] = [];
    
    for (const sub of subscriptions) {
      const symbol = this.normalizeSymbol(sub.symbol).toLowerCase();
      this.subscriptions.add(sub.symbol);
      
      for (const channel of sub.channels) {
        switch (channel) {
          case 'ticker':
            streams.push(`${symbol}@ticker`);
            break;
          case 'orderbook':
            streams.push(`${symbol}@depth`);
            break;
          case 'trades':
            streams.push(`${symbol}@trade`);
            break;
        }
      }
    }

    const subscribeMessage = {
      method: 'SUBSCRIBE',
      params: streams,
      id: Date.now()
    };

    this.wsClient.send(JSON.stringify(subscribeMessage));
    this.logger.info('Subscribed to market data streams', { streams });
  }

  /**
   * Unsubscribe from market data
   */
  public async unsubscribeFromMarketData(symbols: string[]): Promise<void> {
    if (!this.wsClient || this.wsClient.readyState !== WebSocket.OPEN) {
      return;
    }

    const streams: string[] = [];
    
    for (const symbol of symbols) {
      const normalizedSymbol = this.normalizeSymbol(symbol).toLowerCase();
      this.subscriptions.delete(symbol);
      
      streams.push(
        `${normalizedSymbol}@ticker`,
        `${normalizedSymbol}@depth`,
        `${normalizedSymbol}@trade`
      );
    }

    const unsubscribeMessage = {
      method: 'UNSUBSCRIBE',
      params: streams,
      id: Date.now()
    };

    this.wsClient.send(JSON.stringify(unsubscribeMessage));
    this.logger.info('Unsubscribed from market data streams', { streams });
  }

  /**
   * Get ticker information
   */
  public async getTicker(symbol: string): Promise<Ticker> {
    try {
      await this.checkRateLimit('ticker');
      const response = await this.restClient.get('/v3/ticker/24hr', {
        params: { symbol: this.normalizeSymbol(symbol) }
      });

      const data = response.data;
      return {
        symbol,
        bid: data.bidPrice,
        ask: data.askPrice,
        last: data.lastPrice,
        volume: data.volume,
        change: data.priceChange,
        changePercent: data.priceChangePercent,
        timestamp: Date.now()
      };
    } catch (error) {
      this.logger.error(`Failed to get ticker for ${symbol}:`, error);
      throw error;
    }
  }

  /**
   * Get order book
   */
  public async getOrderBook(symbol: string, limit: number = 100): Promise<OrderBook> {
    try {
      await this.checkRateLimit('orderBook');
      const response = await this.restClient.get('/v3/depth', {
        params: { 
          symbol: this.normalizeSymbol(symbol),
          limit: Math.min(limit, 5000)
        }
      });

      return {
        symbol,
        bids: response.data.bids,
        asks: response.data.asks,
        timestamp: Date.now()
      };
    } catch (error) {
      this.logger.error(`Failed to get order book for ${symbol}:`, error);
      throw error;
    }
  }

  /**
   * Get recent trades
   */
  public async getTrades(symbol: string, limit: number = 500): Promise<Trade[]> {
    try {
      await this.checkRateLimit('trades');
      const response = await this.restClient.get('/v3/trades', {
        params: { 
          symbol: this.normalizeSymbol(symbol),
          limit: Math.min(limit, 1000)
        }
      });

      return response.data.map((trade: any) => ({
        id: trade.id.toString(),
        symbol,
        side: trade.isBuyerMaker ? 'sell' : 'buy',
        amount: trade.qty,
        price: trade.price,
        timestamp: trade.time
      }));
    } catch (error) {
      this.logger.error(`Failed to get trades for ${symbol}:`, error);
      throw error;
    }
  }

  /**
   * Normalize symbol format for Binance (remove slash)
   */
  protected normalizeSymbol(symbol: string): string {
    return symbol.replace('/', '').toUpperCase();
  }

  /**
   * Denormalize symbol format from Binance
   */
  protected denormalizeSymbol(symbol: string): string {
    // Convert BTCUSDT to BTC/USDT
    const match = symbol.match(/^([A-Z]+)(USDT|USD|BTC|ETH|BNB)$/);
    if (match) {
      return `${match[1]}/${match[2]}`;
    }
    return symbol;
  }

  /**
   * Place a new order
   */
  public async placeOrder(order: OrderRequest): Promise<Order> {
    try {
      this.validateOrderRequest(order);
      await this.checkRateLimit('placeOrder');

      const params: any = {
        symbol: this.normalizeSymbol(order.symbol),
        side: order.side.toUpperCase(),
        type: order.type.toUpperCase(),
        quantity: order.amount,
        newClientOrderId: order.clientOrderId || this.generateClientOrderId()
      };

      if (order.type === 'limit') {
        params.price = order.price;
        params.timeInForce = order.timeInForce || 'GTC';
      }

      if (order.stopPrice) {
        params.stopPrice = order.stopPrice;
      }

      const response = await this.restClient.post('/v3/order', null, { params });
      const data: BinanceOrderData = response.data;

      const orderResult: Order = {
        id: data.orderId.toString(),
        clientOrderId: data.clientOrderId,
        symbol: order.symbol,
        side: order.side,
        type: order.type,
        amount: data.origQty,
        price: data.price,
        filled: data.executedQty,
        remaining: (parseFloat(data.origQty) - parseFloat(data.executedQty)).toString(),
        status: this.mapOrderStatus(data.status),
        timestamp: data.transactTime,
        fee: data.fills ? {
          currency: data.fills[0]?.commissionAsset || '',
          cost: data.fills?.reduce((sum, fill) => sum + parseFloat(fill.commission), 0).toString() || '0'
        } : undefined
      };

      this.handleOrderUpdate(orderResult);
      return orderResult;
    } catch (error) {
      this.logger.error(`Failed to place order:`, error);
      throw error;
    }
  }

  /**
   * Cancel an existing order
   */
  public async cancelOrder(request: CancelOrderRequest): Promise<boolean> {
    try {
      await this.checkRateLimit('cancelOrder');

      const params: any = {
        symbol: this.normalizeSymbol(request.symbol),
        orderId: request.orderId
      };

      if (request.clientOrderId) {
        params.origClientOrderId = request.clientOrderId;
      }

      await this.restClient.delete('/v3/order', { params });
      this.logger.info(`Order cancelled successfully`, { orderId: request.orderId });
      return true;
    } catch (error) {
      this.logger.error(`Failed to cancel order ${request.orderId}:`, error);
      return false;
    }
  }

  /**
   * Cancel all orders for a symbol
   */
  public async cancelAllOrders(symbol?: string): Promise<boolean> {
    try {
      await this.checkRateLimit('cancelAllOrders');

      const params: any = {};
      if (symbol) {
        params.symbol = this.normalizeSymbol(symbol);
      }

      await this.restClient.delete('/v3/openOrders', { params });
      this.logger.info(`All orders cancelled successfully`, { symbol });
      return true;
    } catch (error) {
      this.logger.error(`Failed to cancel all orders:`, error);
      return false;
    }
  }

  /**
   * Get order status
   */
  public async getOrder(orderId: string, symbol: string): Promise<Order> {
    try {
      await this.checkRateLimit('getOrder');

      const response = await this.restClient.get('/v3/order', {
        params: {
          symbol: this.normalizeSymbol(symbol),
          orderId
        }
      });

      const data = response.data;
      return {
        id: data.orderId.toString(),
        clientOrderId: data.clientOrderId,
        symbol,
        side: data.side.toLowerCase(),
        type: data.type.toLowerCase(),
        amount: data.origQty,
        price: data.price,
        filled: data.executedQty,
        remaining: (parseFloat(data.origQty) - parseFloat(data.executedQty)).toString(),
        status: this.mapOrderStatus(data.status),
        timestamp: data.time
      };
    } catch (error) {
      this.logger.error(`Failed to get order ${orderId}:`, error);
      throw error;
    }
  }

  /**
   * Get open orders
   */
  public async getOpenOrders(symbol?: string): Promise<Order[]> {
    try {
      await this.checkRateLimit('getOpenOrders');

      const params: any = {};
      if (symbol) {
        params.symbol = this.normalizeSymbol(symbol);
      }

      const response = await this.restClient.get('/v3/openOrders', { params });

      return response.data.map((order: any) => ({
        id: order.orderId.toString(),
        clientOrderId: order.clientOrderId,
        symbol: symbol || this.denormalizeSymbol(order.symbol),
        side: order.side.toLowerCase(),
        type: order.type.toLowerCase(),
        amount: order.origQty,
        price: order.price,
        filled: order.executedQty,
        remaining: (parseFloat(order.origQty) - parseFloat(order.executedQty)).toString(),
        status: this.mapOrderStatus(order.status),
        timestamp: order.time
      }));
    } catch (error) {
      this.logger.error('Failed to get open orders:', error);
      throw error;
    }
  }

  /**
   * Get order history
   */
  public async getOrderHistory(symbol?: string, limit: number = 500): Promise<Order[]> {
    try {
      await this.checkRateLimit('getOrderHistory');

      const params: any = {
        limit: Math.min(limit, 1000)
      };

      if (symbol) {
        params.symbol = this.normalizeSymbol(symbol);
      }

      const response = await this.restClient.get('/v3/allOrders', { params });

      return response.data.map((order: any) => ({
        id: order.orderId.toString(),
        clientOrderId: order.clientOrderId,
        symbol: symbol || this.denormalizeSymbol(order.symbol),
        side: order.side.toLowerCase(),
        type: order.type.toLowerCase(),
        amount: order.origQty,
        price: order.price,
        filled: order.executedQty,
        remaining: (parseFloat(order.origQty) - parseFloat(order.executedQty)).toString(),
        status: this.mapOrderStatus(order.status),
        timestamp: order.time
      }));
    } catch (error) {
      this.logger.error('Failed to get order history:', error);
      throw error;
    }
  }

  /**
   * Get account balances
   */
  public async getBalances(): Promise<Balance[]> {
    try {
      await this.checkRateLimit('getBalances');

      const response = await this.restClient.get('/v3/account');

      return response.data.balances
        .filter((balance: any) => parseFloat(balance.free) > 0 || parseFloat(balance.locked) > 0)
        .map((balance: any) => ({
          currency: balance.asset,
          total: (parseFloat(balance.free) + parseFloat(balance.locked)).toString(),
          available: balance.free,
          locked: balance.locked
        }));
    } catch (error) {
      this.logger.error('Failed to get balances:', error);
      throw error;
    }
  }

  /**
   * Get current positions (Binance spot doesn't have positions, return empty array)
   */
  public async getPositions(): Promise<Position[]> {
    // Binance spot trading doesn't have positions
    return [];
  }

  /**
   * Check if exchange supports a trading pair
   */
  public async supportsSymbol(symbol: string): Promise<boolean> {
    try {
      const exchangeInfo = await this.getExchangeInfo();
      return exchangeInfo.symbols.some(s => s.symbol === symbol && s.status === 'TRADING');
    } catch (error) {
      this.logger.error(`Failed to check symbol support for ${symbol}:`, error);
      return false;
    }
  }

  /**
   * Get minimum order size for a symbol
   */
  public async getMinOrderSize(symbol: string): Promise<string> {
    try {
      const exchangeInfo = await this.getExchangeInfo();
      const symbolInfo = exchangeInfo.symbols.find(s => s.symbol === symbol);
      return symbolInfo?.minOrderSize || '0.001';
    } catch (error) {
      this.logger.error(`Failed to get min order size for ${symbol}:`, error);
      return '0.001';
    }
  }

  /**
   * Get trading fees for a symbol
   */
  public async getTradingFees(symbol: string): Promise<{ maker: string; taker: string }> {
    try {
      await this.checkRateLimit('getTradingFees');

      const response = await this.restClient.get('/v3/tradeFee', {
        params: { symbol: this.normalizeSymbol(symbol) }
      });

      const feeData = response.data.tradeFee?.[0];
      return {
        maker: feeData?.makerCommission || '0.001',
        taker: feeData?.takerCommission || '0.001'
      };
    } catch (error) {
      this.logger.error(`Failed to get trading fees for ${symbol}:`, error);
      // Return default fees if API call fails
      return { maker: '0.001', taker: '0.001' };
    }
  }

  /**
   * Test connection to Binance API
   */
  public async testConnection(): Promise<boolean> {
    try {
      await this.restClient.get('/v3/ping');
      return true;
    } catch (error) {
      this.logger.error('Binance connection test failed:', error);
      return false;
    }
  }

  /**
   * Map Binance order status to standard format
   */
  private mapOrderStatus(status: string): 'open' | 'closed' | 'canceled' | 'expired' | 'rejected' {
    switch (status) {
      case 'NEW':
      case 'PARTIALLY_FILLED':
        return 'open';
      case 'FILLED':
        return 'closed';
      case 'CANCELED':
        return 'canceled';
      case 'EXPIRED':
        return 'expired';
      case 'REJECTED':
        return 'rejected';
      default:
        return 'open';
    }
  }
}
