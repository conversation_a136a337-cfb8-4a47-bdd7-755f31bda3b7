{"name": "alphamarketmaker-dashboard", "version": "1.0.0", "description": "Real-time cryptocurrency market making dashboard", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.2", "socket.io-client": "^4.7.2", "axios": "^1.4.0", "recharts": "^2.7.2", "lucide-react": "^0.263.1", "clsx": "^2.0.0", "tailwind-merge": "^1.14.0", "@headlessui/react": "^1.7.15", "@heroicons/react": "^2.0.18", "framer-motion": "^10.12.18", "react-hot-toast": "^2.4.1", "react-hook-form": "^7.45.2", "@hookform/resolvers": "^3.1.1", "zod": "^3.21.4", "date-fns": "^2.30.0", "numeral": "^2.0.6"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/numeral": "^2.0.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.27", "tailwindcss": "^3.3.3", "typescript": "^5.0.2", "vite": "^4.4.5", "vitest": "^0.34.1", "@vitest/ui": "^0.34.1", "jsdom": "^22.1.0"}, "keywords": ["cryptocurrency", "market-making", "trading", "dashboard", "react", "typescript", "real-time"], "author": "AlphaMarketMaker Team", "license": "MIT", "homepage": "https://alphamarketmaker.com", "repository": {"type": "git", "url": "https://github.com/yourusername/AlphaMarketMaker.git", "directory": "web"}, "bugs": {"url": "https://github.com/yourusername/AlphaMarketMaker/issues"}}